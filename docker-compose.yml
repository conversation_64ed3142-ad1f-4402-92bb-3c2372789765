services:
  siift-next:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_ADMIN_API_URL=https://siift-backend.up.railway.app
        - NEXT_PUBLIC_API_URL=/api
        - NEXT_PUBLIC_APP_NAME=Siift
        - NEXT_PUBLIC_APP_URL=http://localhost:3000
        - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk
        - NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/login
        - NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/register
        - NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/user-dashboard
        - NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/user-dashboard
        - NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/user-dashboard
        - NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/user-dashboard
        - NEXT_PUBLIC_POSTHOG_KEY=phc_2oQSSzXgGcCmTZ0YigOv7yq9RfH49WlVktnZ6HC2vQA
        - NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
        - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51S4rGA3YtpK1aoNIBqOkhX6CI4jgq31ikF43cPqEnykBxSdVZeYypVq8oRnufetTHErKoXcneGCdU4xGf3zRZFCc00Yfa5fD5X
        - JWT_SECRET=your-super-secret-jwt-key-change-in-production
        - CLERK_SECRET_KEY=sk_test_wInjtWLWbkIxVlwL2Pq4A8vsqLzzxKVw6Bow2ai3nF
        - CLERK_JWT_ISSUER_DOMAIN=https://true-snail-46.clerk.accounts.dev
        - CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_from_clerk_dashboard
        - STRIPE_SECRET_KEY=sk_test_51S4rGA3YtpK1aoNIDaj3UfJ7DGcXcF6Mkfb3XFGwuoFvYjvK17MEvL4nYjZefbD3Pg3Xf2QG1eEgpEDdwjFQu8Z900xzjIZ8vt
        - STRIPE_WEBHOOK_SECRET=whsec_e5e4dccd3187f6e141fe0926317589ba4907028f1855137eb6b3ee4692cdb429
    ports:
      - "3000:3000"
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      # Public environment variables (baked into build)
      - NEXT_PUBLIC_ADMIN_API_URL=https://siift-backend.up.railway.app
      - NEXT_PUBLIC_API_URL=/api
      - NEXT_PUBLIC_APP_NAME=Siift
      - NEXT_PUBLIC_APP_URL=http://localhost:3000
      - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk
      - NEXT_PUBLIC_CLERK_SIGN_IN_URL=/auth/login
      - NEXT_PUBLIC_CLERK_SIGN_UP_URL=/auth/register
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/user-dashboard
      - NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/user-dashboard
      - NEXT_PUBLIC_CLERK_SIGN_IN_FALLBACK_REDIRECT_URL=/user-dashboard
      - NEXT_PUBLIC_CLERK_SIGN_UP_FALLBACK_REDIRECT_URL=/user-dashboard
      - NEXT_PUBLIC_POSTHOG_KEY=phc_2oQSSzXgGcCmTZ0YigOv7yq9RfH49WlVktnZ6HC2vQA
      - NEXT_PUBLIC_POSTHOG_HOST=https://us.i.posthog.com
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_51S4rGA3YtpK1aoNIBqOkhX6CI4jgq31ikF43cPqEnykBxSdVZeYypVq8oRnufetTHErKoXcneGCdU4xGf3zRZFCc00Yfa5fD5X
      # Server-side only environment variables
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
      - CLERK_SECRET_KEY=sk_test_wInjtWLWbkIxVlwL2Pq4A8vsqLzzxKVw6Bow2ai3nF
      - CLERK_JWT_ISSUER_DOMAIN=https://true-snail-46.clerk.accounts.dev
      - CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret_from_clerk_dashboard
      - STRIPE_SECRET_KEY=sk_test_51S4rGA3YtpK1aoNIDaj3UfJ7DGcXcF6Mkfb3XFGwuoFvYjvK17MEvL4nYjZefbD3Pg3Xf2QG1eEgpEDdwjFQu8Z900xzjIZ8vt
      - STRIPE_WEBHOOK_SECRET=whsec_e5e4dccd3187f6e141fe0926317589ba4907028f1855137eb6b3ee4692cdb429
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:3000/api/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
