"use client";

import { useState } from "react";
import { stripePromise } from "@/lib/stripe-client";
import { useCreditsStore } from "@/stores/creditsStore";
import { useToast } from "@/hooks/useToast";

interface PaymentData {
  amount: number;
  credits: number;
}

export function useStripePayment() {
  const [isProcessing, setIsProcessing] = useState(false);
  const { addCredits } = useCreditsStore();
  const { success, error } = useToast();

  const processPayment = async ({ amount, credits }: PaymentData) => {
    if (isProcessing) return;

    setIsProcessing(true);

    try {
      // Create payment intent
      const response = await fetch("/api/stripe/create-payment-intent", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          amount,
          credits,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create payment intent");
      }

      // For demo purposes, simulate successful payment
      // In production, you would handle the actual Stripe payment flow here
      console.log("Payment simulation for:", { amount, credits });

      // Simulate payment processing delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Add credits to store
      addCredits(credits);

      success(`Successfully added ${credits} credits to your account.`);

      return { success: true };
    } catch (err) {
      console.error("Payment error:", err);
      error(
        err instanceof Error
          ? err.message
          : "Something went wrong. Please try again."
      );
      return { success: false, error: err };
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    processPayment,
    isProcessing,
  };
}
