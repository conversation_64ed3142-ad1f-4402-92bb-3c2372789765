"use client";

import { useQuery } from "@tanstack/react-query";
import { authApi } from "@/lib/api";
import type { Project } from "@/lib/types";

export function useProjects() {
  return useQuery<Project[], Error>({
    queryKey: ["projects"],
    queryFn: () => authApi.getProjects(),
    staleTime: 60_000,
    // Always refetch when the component mounts so newly created projects appear
    refetchOnMount: "always",
    // Also refetch on window focus to keep the list fresh when returning to the tab
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook for fetching projects with optional limit and sorting
 * Optimized for dashboard use with configurable limits
 */
export function useProjectsLimited(limit?: number, sortBy: 'updated' | 'created' = 'updated') {
  return useQuery<Project[], Error>({
    queryKey: ["projects", "limited", limit, sortBy],
    queryFn: async () => {
      const projects = await authApi.getProjects();
      if (!Array.isArray(projects)) return [];
      
      // Sort projects based on sortBy parameter
      const sorted = [...projects].sort((a, b) => {
        const dateA = new Date(sortBy === 'updated' ? (a.updatedAt || a.createdAt) : a.createdAt);
        const dateB = new Date(sortBy === 'updated' ? (b.updatedAt || b.createdAt) : b.createdAt);
        return dateB.getTime() - dateA.getTime();
      });
      
      // Apply limit if specified
      return limit ? sorted.slice(0, limit) : sorted;
    },
    staleTime: 60_000,
    refetchOnMount: "always",
    refetchOnWindowFocus: true,
  });
}

export function useProject(projectId: string | undefined) {
  return useQuery<Project, Error>({
    queryKey: ["project", projectId],
    queryFn: () => authApi.getProject(projectId as string),
    enabled: !!projectId,
    staleTime: 60_000,
  });
}
