"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";

export function useSiiftTable(projectId: string | undefined) {
  return useQuery<any, Error>({
    queryKey: ["siift-table", projectId],
    queryFn: async () => {
      const res = await fetch(`/api/projects/${projectId}/siift-table`);
      if (!res.ok) throw new Error("Failed to fetch SIIFT table");
      return res.json();
    },
    enabled: !!projectId,
    staleTime: 30_000,
  });
}

export function useSiiftTableDetailed(projectId: string | undefined) {
  return useQuery<any, Error>({
    queryKey: ["siift-table-detailed", projectId],
    queryFn: async () => {
      const res = await fetch(`/api/projects/${projectId}/siift-table/detailed`, {
        credentials: "include",
      });
      if (!res.ok) throw new Error("Failed to fetch SIIFT table detailed");
      return res.json();
    },
    enabled: !!projectId,
    staleTime: 30_000,
  });
}

export function useProjectActionItems(
  projectId: string | undefined,
  filters?: { type?: string; status?: string }
) {
  return useQuery<any, Error>({
    queryKey: [
      "project-action-items",
      projectId,
      filters?.type || "",
      filters?.status || "",
    ],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (filters?.type) params.set("type", filters.type);
      if (filters?.status) params.set("status", filters.status);
      const res = await fetch(
        `/api/projects/${projectId}/action-items${params.toString() ? `?${params.toString()}` : ""}`,
        { credentials: "include" }
      );
      if (!res.ok) return { data: [] };
      const json = await res.json().catch(() => ({ data: [] }));
      const list = Array.isArray(json)
        ? json
        : Array.isArray(json?.data)
        ? json.data
        : Array.isArray(json?.items)
        ? json.items
        : [];
      return list;
    },
    enabled: !!projectId,
    staleTime: 15_000,
  });
}

export function useInvalidateSiift() {
  const qc = useQueryClient();
  return (projectId: string) => {
    qc.invalidateQueries({ queryKey: ["siift-table", projectId] });
    qc.invalidateQueries({ queryKey: ["siift-table-detailed", projectId] });
    // Also refresh sections/topics that the project page renders
    qc.invalidateQueries({ queryKey: ["business-sections", projectId] });
    qc.invalidateQueries({ queryKey: ["topics", projectId] });
  };
}



