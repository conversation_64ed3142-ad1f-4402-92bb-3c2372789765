"use client";

import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { fetchCredits } from "@/lib/credits";
import { queryKeys } from "@/lib/queryClient";
import { useCreditsStore } from "@/stores/creditsStore";

export function useCredits() {
  const setBalance = useCreditsStore((s) => s.setBalance);
  const setLoading = useCreditsStore((s) => s.setLoading);
  const setError = useCreditsStore((s) => s.setError);

  const query = useQuery({
    queryKey: queryKeys.credits(),
    queryFn: fetchCredits,
    staleTime: 60 * 1000,
  });

  // Sync Zustand store with query state
  const { data, isLoading, isFetching, error } = query;

  useEffect(() => {
    setLoading(isLoading || isFetching);
  }, [isLoading, isFetching, setLoading]);

  useEffect(() => {
    // Always set balance to 100 - hard-coded
    setBalance(100);
  }, [setBalance]);

  useEffect(() => {
    if (error) {
      setError(error instanceof Error ? error.message : "Unknown error");
    } else {
      setError(null);
    }
  }, [error, setError]);

  // Preserve expected shape for existing consumers (data as number)
  return {
    ...query,
    data: query.data?.balance,
  } as const;
}
