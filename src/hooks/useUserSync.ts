"use client";

import { useUser } from "@clerk/nextjs";
import { useEffect, useState } from "react";
import { toast } from "sonner";

export function useUserSync() {
  const { user, isLoaded } = useUser();
  const [isUserSynced, setIsUserSynced] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const syncUserToBackend = async (clerkUser: any) => {
    if (!clerkUser || isSyncing) return;

    setIsSyncing(true);

    try {
      // Derive robust first/last names if missing from Clerk object
      const full = (clerkUser.fullName || "").trim();
      const parts = full ? full.split(/\s+/) : [];
      const derivedFirst = parts[0] || "";
      const derivedLast = parts.slice(1).join(" ") || "";
      const emailLocal =
        (clerkUser.emailAddresses?.[0]?.emailAddress || "").split("@")[0] || "";

      const userData = {
        email: clerkUser.emailAddresses[0]?.emailAddress || "",
        firstName: clerkUser.firstName || derivedFirst || emailLocal || "User",
        lastName: clerkUser.lastName || derivedLast || "",
        clerkId: clerkUser.id,
        role: "user",
        status: "active",
        avatarUrl: clerkUser.imageUrl || "",
        bio: "",
        timezone: "UTC",
        preferences: {
          notifications: true,
          theme: "system",
          language: "en",
        },
      };

      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

      // First, check if user already exists
      try {
        const checkResponse = await fetch(
          `${backendUrl}/api/users/clerk/${clerkUser.id}`
        );
        if (checkResponse.ok) {
          // User already exists
          setIsUserSynced(true);
          setIsSyncing(false);
          return;
        }
      } catch (error) {
        // User doesn't exist, continue with creation
      }

      // Create user in backend
      const response = await fetch(`${backendUrl}/api/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      if (response.ok) {
        console.log("User successfully synced to backend");
        setIsUserSynced(true);
        toast.success("Account setup complete!");
      } else {
        const errorText = await response.text();
        console.error("Failed to sync user to backend:", errorText);
        toast.error("Account setup failed. Please contact support.");
      }
    } catch (error) {
      console.error("Error syncing user to backend:", error);
      toast.error("Account setup failed. Please contact support.");
    } finally {
      setIsSyncing(false);
    }
  };

  useEffect(() => {
    if (isLoaded && user && !isUserSynced && !isSyncing) {
      syncUserToBackend(user);
    }
  }, [isLoaded, user, isUserSynced, isSyncing]);

  return {
    isUserSynced,
    isSyncing,
    syncUserToBackend,
  };
}
