import Stripe from "stripe";

// Only validate STRIPE_SECRET_KEY at runtime, not during build
const getStripeSecretKey = () => {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error("STRIPE_SECRET_KEY is not set");
  }
  return process.env.STRIPE_SECRET_KEY;
};

// Create stripe instance lazily to avoid build-time errors
let stripeInstance: Stripe | null = null;

export const stripe = new Proxy({} as Stripe, {
  get(target, prop) {
    if (!stripeInstance) {
      stripeInstance = new Stripe(getStripeSecretKey(), {
        apiVersion: "2025-08-27.basil",
      });
    }
    return stripeInstance[prop as keyof Stripe];
  },
});

export const STRIPE_PUBLISHABLE_KEY =
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (!STRIPE_PUBLISHABLE_KEY) {
  throw new Error("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY is not set");
}
