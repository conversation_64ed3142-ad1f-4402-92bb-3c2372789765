// Mock credits API using stored data from the credits store
// This returns the current stored balance and provides functions to update it

export interface CreditsResponse {
  balance: number;
}

// Import the store to access current balance
import { useCreditsStore } from "@/stores/creditsStore";

export async function fetchCredits(): Promise<CreditsResponse> {
  // Simulate network latency
  await new Promise((r) => setTimeout(r, 200));

  // Always return 100 credits - hard-coded
  return { balance: 100 };
}

// Function to consume credits (decrease balance)
export async function consumeCredits(amount: number): Promise<CreditsResponse> {
  // Simulate network latency
  await new Promise((r) => setTimeout(r, 200));

  const store = useCreditsStore.getState();
  const currentBalance = store.balance;
  const newBalance = Math.max(0, currentBalance - amount); // Prevent negative balance

  // Update the store
  store.setBalance(newBalance);

  return { balance: newBalance };
}

// Function to add credits (increase balance)
export async function addCredits(amount: number): Promise<CreditsResponse> {
  // Simulate network latency
  await new Promise((r) => setTimeout(r, 200));

  const store = useCreditsStore.getState();
  const currentBalance = store.balance;
  const newBalance = currentBalance + amount;

  // Update the store
  store.setBalance(newBalance);

  return { balance: newBalance };
}
