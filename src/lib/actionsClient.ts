import { apiClient } from "@/lib/apiClient";

type RunActionInput = {
  actionKey: string;
  projectId: string;
  userId: string;
  input: Record<string, any>;
};

class ActionsClient {
  async run(body: RunActionInput) {
    return apiClient.post("/api/actions/run", body);
  }

  async suggestActions(params: {
    projectId: string;
    userId: string;
    sessionId: string;
    topicId: number | string;
  }) {
    const { projectId, userId, sessionId, topicId } = params;
    return this.run({
      actionKey: "suggest_actions",
      projectId,
      userId,
      input: {
        sessionId,
        topicId: Number(topicId),
      },
    });
  }

  async entryResearch(params: {
    projectId: string;
    userId: string;
    sessionId: string;
    topicId: number | string;
    entryId: number | string;
    ideaText: string;
    customPrompt?: string;
  }) {
    const { projectId, userId, sessionId, topicId, entryId, ideaText, customPrompt } = params;
    return this.run({
      actionKey: "entry_research",
      projectId,
      userId,
      input: {
        sessionId,
        topicId: Number(topicId),
        entryId: Number(entryId),
        ideaText,
        ...(customPrompt && { customPrompt }),
      },
    });
  }

  async syncSiift(params: { projectId: string; userId: string; sessionId: string }) {
    const { projectId, userId, sessionId } = params;
    return this.run({
      actionKey: "sync_siift",
      projectId,
      userId,
      input: { sessionId },
    });
  }
}

export const actionsClient = new ActionsClient();


