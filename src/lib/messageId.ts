/**
 * Utility functions for generating unique message IDs
 */

let messageIdCounter = 0;

/**
 * Generates a unique message ID with timestamp and counter
 * Format: {prefix}-{timestamp}-{counter}
 */
export function generateMessageId(prefix: string = "msg"): string {
  const timestamp = Date.now();
  const counter = ++messageIdCounter;
  return `${prefix}-${timestamp}-${counter}`;
}

/**
 * Generates a unique AI message ID
 */
export function generateAiMessageId(): string {
  return generateMessageId("ai");
}

/**
 * Generates a unique user message ID
 */
export function generateUserMessageId(): string {
  return generateMessageId("user");
}

/**
 * Generates a unique onboarding message ID
 */
export function generateOnboardingMessageId(): string {
  return generateMessageId("onboarding");
}

/**
 * Generates a unique welcome message ID
 */
export function generateWelcomeMessageId(): string {
  return generateMessageId("welcome");
}

/**
 * Ensures message IDs are unique by adding a suffix if needed
 */
export function ensureUniqueMessageId(
  id: string,
  existingIds: Set<string>
): string {
  if (!existingIds.has(id)) {
    return id;
  }

  let counter = 1;
  let uniqueId = `${id}-${counter}`;

  while (existingIds.has(uniqueId)) {
    counter++;
    uniqueId = `${id}-${counter}`;
  }

  return uniqueId;
}

/**
 * Deduplicates messages by ID, keeping the first occurrence
 */
export function deduplicateMessages<T extends { id: string }>(
  messages: T[]
): T[] {
  const seen = new Set<string>();
  return messages.filter((message) => {
    if (seen.has(message.id)) {
      return false;
    }
    seen.add(message.id);
    return true;
  });
}
