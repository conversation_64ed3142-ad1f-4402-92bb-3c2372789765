import { create } from "zustand";
import { devtools } from "zustand/middleware";

type ActionRunState = {
  isRunning: boolean;
  progressPct?: number;
  lastRunId?: string;
  lastError?: string | null;
  actionKey?: string;
};

type ActionsStore = {
  byTargetKey: Record<string, ActionRunState>;
  setRunning: (targetKey: string, actionKey: string) => void;
  setProgress: (targetKey: string, progressPct: number) => void;
  setCompleted: (targetKey: string) => void;
  setFailed: (targetKey: string, error: string) => void;
  clear: (targetKey?: string) => void;
};

export const useActionsStore = create<ActionsStore>()(
  devtools(
    (set) => ({
      byTargetKey: {},
      setRunning: (targetKey, actionKey) =>
        set((state) => ({
          byTargetKey: {
            ...state.byTargetKey,
            [targetKey]: {
              ...(state.byTargetKey[targetKey] || {}),
              isRunning: true,
              actionKey,
              lastError: null,
            },
          },
        })),
      setProgress: (targetKey, progressPct) =>
        set((state) => ({
          byTargetKey: {
            ...state.byTargetKey,
            [targetKey]: {
              ...(state.byTargetKey[targetKey] || {}),
              isRunning: true,
              progressPct,
            },
          },
        })),
      setCompleted: (targetKey) =>
        set((state) => ({
          byTargetKey: {
            ...state.byTargetKey,
            [targetKey]: {
              ...(state.byTargetKey[targetKey] || {}),
              isRunning: false,
            },
          },
        })),
      setFailed: (targetKey, error) =>
        set((state) => ({
          byTargetKey: {
            ...state.byTargetKey,
            [targetKey]: {
              ...(state.byTargetKey[targetKey] || {}),
              isRunning: false,
              lastError: error,
            },
          },
        })),
      clear: (targetKey) =>
        set((state) => {
          if (!targetKey) return { byTargetKey: {} } as any;
          const next = { ...state.byTargetKey };
          delete next[targetKey];
          return { byTargetKey: next } as any;
        }),
    }),
    { name: "actions-store" }
  )
);


