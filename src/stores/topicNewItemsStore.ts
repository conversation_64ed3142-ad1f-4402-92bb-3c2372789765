import { create } from "zustand";
import { devtools } from "zustand/middleware";

type TopicNewItemsState = {
  /** Map of topicId -> whether it currently has unseen new items */
  newByTopicId: Record<string, boolean>;
  /** Map of topicId -> last time user opened/viewed the topic (ms since epoch) */
  lastSeenByTopicId: Record<string, number>;

  /** Mark a single topic as having new items */
  markTopicHasNew: (topicId: string) => void;
  /** Mark multiple topics as having new items */
  markTopicsHasNew: (topicIds: string[]) => void;
  /** Clear the new flag for a topic (e.g., after opening) */
  clearTopicNew: (topicId: string) => void;
  /** Convenience boolean accessor */
  hasNewForTopic: (topicId: string) => boolean;
  /** Record that a topic was opened and clear its new flag */
  markTopicOpened: (topicId: string) => void;
};

export const useTopicNewItemsStore = create<TopicNewItemsState>()(
  devtools(
    (set, get) => ({
      newByTopicId: {},
      lastSeenByTopicId: {},

      markTopicHasNew: (topicId: string) =>
        set((state) => ({
          newByTopicId: { ...state.newByTopicId, [topicId]: true },
        })),

      markTopicsHasNew: (topicIds: string[]) =>
        set((state) => {
          const updated = { ...state.newByTopicId };
          topicIds.forEach((id) => {
            updated[id] = true;
          });
          return { newByTopicId: updated };
        }),

      clearTopicNew: (topicId: string) =>
        set((state) => {
          const updated = { ...state.newByTopicId };
          delete updated[topicId];
          return { newByTopicId: updated };
        }),

      hasNewForTopic: (topicId: string) => Boolean(get().newByTopicId[topicId]),

      markTopicOpened: (topicId: string) => {
        const now = Date.now();
        set((state) => ({
          lastSeenByTopicId: { ...state.lastSeenByTopicId, [topicId]: now },
          newByTopicId: { ...state.newByTopicId, [topicId]: false },
        }));
      },
    }),
    {
      name: "topic-new-items-store",
    }
  )
);

/**
 * Usage examples (from chat handlers or SSE updates):
 *
 * // Mark one topic as having new items
 * useTopicNewItemsStore.getState().markTopicHasNew(topicId);
 *
 * // Mark many topics at once
 * useTopicNewItemsStore.getState().markTopicsHasNew(topicIds);
 *
 * // Clear when user opens the topic detail
 * useTopicNewItemsStore.getState().markTopicOpened(topicId);
 */
