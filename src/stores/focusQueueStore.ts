"use client";

import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface FocusItem {
  key: string;
  durationMs: number;
  addedAt: number;
  level: number;
}

interface FocusQueueState {
  current: FocusItem | null;
  queue: FocusItem[];
  timerId: ReturnType<typeof setTimeout> | null;
  defaultDurationMs: number;

  // Actions
  enqueue: (key: string, durationMs?: number, level?: number) => void;
  enqueueMany: (
    keys: string[],
    durationMs?: number,
    staggerMs?: number,
    level?: number
  ) => void;
  acknowledge: (key: string) => void;
  clearAll: () => void;
  isFocused: (key: string) => boolean;
}

export const useFocusQueueStore = create<FocusQueueState>()(
  devtools(
    (set, get) => ({
      current: null,
      queue: [],
      timerId: null,
      defaultDurationMs: 2000,

      enqueue: (key: string, durationMs?: number, level?: number) => {
        const normalizedKey = (key || "").toString().trim();
        if (!normalizedKey) return;
        const item: FocusItem = {
          key: normalizedKey,
          durationMs: Number.isFinite(durationMs as number)
            ? (durationMs as number)
            : get().defaultDurationMs,
          addedAt: Date.now(),
          level: Number.isFinite(level as number) ? (level as number) : 0,
        };

        const { current, queue } = get();
        // If nothing is focused, start immediately
        if (!current) {
          set({ current: item });
          // Schedule automatic advance
          const tid = setTimeout(() => {
            // Advance only if still the current item
            const s = get();
            if (
              s.current &&
              s.current.key === item.key &&
              s.current.addedAt === item.addedAt
            ) {
              // Clear current then move to next
              set({ current: null, timerId: null });
              // Start next if any
              const next = get().queue[0];
              if (next) {
                // Remove the queued head and start it
                set((prev) => ({ queue: prev.queue.slice(1) }));
                get().enqueue(next.key, next.durationMs, next.level);
              }
            }
          }, item.durationMs);
          set({ timerId: tid });
          return;
        }

        // Otherwise push to queue (avoid consecutive duplicates for stability)
        const last = queue[queue.length - 1];
        if (!last || last.key !== item.key) {
          set({ queue: [...queue, item] });
        }
      },

      enqueueMany: (
        keys: string[],
        durationMs?: number,
        staggerMs?: number,
        level?: number
      ) => {
        const d = Number.isFinite(durationMs as number)
          ? (durationMs as number)
          : get().defaultDurationMs;
        const gap = Number.isFinite(staggerMs as number)
          ? (staggerMs as number)
          : 0;
        let delay = 0;
        keys.forEach((k) => {
          const key = (k || "").toString().trim();
          if (!key) return;
          setTimeout(() => get().enqueue(key, d, level), delay);
          delay += gap;
        });
      },

      acknowledge: (key: string) => {
        const target = (key || "").toString().trim();
        if (!target) return;
        const { current, timerId, queue } = get();
        // Remove future occurrences
        const filtered = queue.filter((q) => q.key !== target);
        // If acknowledging the current item, clear and advance
        if (current && current.key === target) {
          if (timerId) clearTimeout(timerId);
          set({ current: null, timerId: null, queue: filtered });
          const next = filtered[0];
          if (next) {
            set({ queue: filtered.slice(1) });
            get().enqueue(next.key, next.durationMs, next.level);
          }
          return;
        }
        // Otherwise just update queue
        set({ queue: filtered });
      },

      clearAll: () => {
        const { timerId } = get();
        if (timerId) clearTimeout(timerId);
        set({ current: null, queue: [], timerId: null });
      },

      isFocused: (key: string) => {
        const target = (key || "").toString().trim();
        const c = get().current;
        return Boolean(c && c.key === target);
      },
    }),
    {
      name: "focus-queue-store",
    }
  )
);
