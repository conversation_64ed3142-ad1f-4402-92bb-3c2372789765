import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type {
  BusinessItemStore,
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import { computeEntryCounts } from "@/utils/entryCounts";

export const useBusinessItemStore = create<BusinessItemStore>()(
  devtools(
    (set) => ({
      selectedItem: null,
      itemDetails: [],
      isLoading: false,
      error: null,

      setSelectedItem: (item) =>
        set((state) => {
          if (state.selectedItem === item) return state;
          console.log("[BusinessItemStore] setSelectedItem", { id: item?.id });
          return { selectedItem: item };
        }),

      setItemDetails: (details) =>
        set((state) => {
          const current = state.itemDetails;
          const changed =
            !Array.isArray(current) ||
            current.length !== details.length ||
            JSON.stringify(current) !== JSON.stringify(details);
          if (!changed) {
            console.log("[BusinessItemStore] setItemDetails noop (no change)");
            return state;
          }
          console.log("[BusinessItemStore] setItemDetails", {
            count: details.length,
          });
          return { itemDetails: details } as Partial<BusinessItemStore> as any;
        }),

      setLoading: (isLoading) =>
        set((state) => {
          if (state.isLoading === isLoading) return state;
          console.log("[BusinessItemStore] setLoading", { isLoading });
          return { isLoading };
        }),

      setError: (error) =>
        set((state) => {
          if (state.error === error) return state;
          console.log("[BusinessItemStore] setError", { error });
          return { error };
        }),

      addItemDetail: (detail) =>
        set((state) => ({
          itemDetails: [...state.itemDetails, detail],
        })),

      updateItemDetail: (id, updates) =>
        set((state) => ({
          itemDetails: state.itemDetails.map((item) =>
            item.id === id ? { ...item, ...updates } : item
          ),
        })),

      removeItemDetail: (id) =>
        set((state) => ({
          itemDetails: state.itemDetails.filter((item) => item.id !== id),
        })),

      // Centralized method to load topic details from store
      loadTopicFromStore: (params) => {
        const {
          sectionId,
          numericTopicId,
          topicId,
          topicTitle,
          topicIcon,
          allTopics = [],
        } = params;

        console.log(
          "[BusinessItemStore] loadTopicFromStore called but needs getTopicEntries function"
        );
        console.log("Use the utility function loadTopicDetails instead");
      },
    }),
    {
      name: "business-item-storage",
    }
  )
);
