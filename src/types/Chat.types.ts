export interface ChatMessage {
  id: string;
  user: string;
  avatar: string;
  message: string;
  timestamp: string;
  isCurrentUser: boolean;
  isOnboarding?: boolean; // Mark messages as onboarding (persist across refreshes)
  cta?: {
    type: 'refetch_topics';
    label?: string;
  };
}

export interface ChatSession {
  id: string;
  projectId: string;
  // any other session-related data
}

export interface ChatStore {
  messages: ChatMessage[];
  chatSession: ChatSession | null;
  isLoading: boolean;
  isStreaming: boolean;
  // Project-specific loading states
  projectLoadingStates: { [projectId: string]: boolean };
  projectStreamingStates: { [projectId: string]: boolean };
  // Scoped chat support (per project, per topic)
  messagesByScope?: { [scopeKey: string]: ChatMessage[] };
  currentScopeKey?: string | null;
  
  // Actions
  setMessages: (messages: ChatMessage[]) => void;
  addMessage: (message: ChatMessage) => void;
  updateLastMessage: (content: string) => void;
  appendToLastMessage: (contentChunk: string) => void;
  addCtaMessage?: (text: string, cta: { type: 'refetch_topics'; label?: string }) => void;
  setChatSession: (session: ChatSession | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsStreaming: (isStreaming: boolean) => void;
  setProjectLoading: (projectId: string, isLoading: boolean) => void;
  setProjectStreaming: (projectId: string, isStreaming: boolean) => void;
  getProjectLoading: (projectId: string) => boolean;
  getProjectStreaming: (projectId: string) => boolean;
  clearChat: () => void;
  // Scope controls
  setScope?: (projectId: string, topicName?: string | null) => void;
  getScopeKey?: (projectId: string, topicName?: string | null) => string;
  
  // Onboarding
  ensureOnboardingMessages?: () => void;
}