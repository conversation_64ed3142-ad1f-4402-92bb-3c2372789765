import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_ADMIN_API_URL ||
  process.env.NEXT_PUBLIC_API_URL ||
  "http://localhost:3002";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, data: [] }, { status: 200 });

    const { id } = await params;
    const search = request.nextUrl.search || "";

    const resp = await fetch(`${BACKEND_URL}/api/projects/${id}/action-items${search}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    if (!resp.ok) {
      // Safest: hide backend shape/availability differences and just return empty list
      return NextResponse.json({ success: true, data: [] }, { status: 200 });
    }

    const data = await resp.json().catch(() => ({ success: true, data: [] }));
    return NextResponse.json(data, { status: 200 });
  } catch (e) {
    // Fail closed but do not break UI
    return NextResponse.json({ success: true, data: [] }, { status: 200 });
  }
}


