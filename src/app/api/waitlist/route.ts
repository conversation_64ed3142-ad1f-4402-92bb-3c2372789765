import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, name, source } = body || {};

    if (!email || typeof email !== "string") {
      return NextResponse.json(
        { success: false, error: "Email is required" },
        { status: 400 }
      );
    }

    try {
      const apiKey = process.env.RESEND_API_KEY;
      const fromEmail = process.env.RESEND_FROM_EMAIL;

      // Debug logging
      console.log("[Waitlist API] Env check:", {
        hasApiKey: !!apiKey,
        hasFromEmail: !!fromEmail,
        apiKeyLength: apiKey?.length,
        fromEmail,
      });

      if (apiKey && fromEmail) {
        const resend = new Resend(apiKey);
        const isBetaRequest = (source || "").toLowerCase().includes("beta");
        const requestType = isBetaRequest
          ? "Beta Access Request"
          : "Waitlist Signup";
        const subject = `New ${requestType}${name ? `: ${name}` : ""}`;
        const lines = [
          `Email: ${email}`,
          `Name: ${name || "-"}`,
          `Source: ${source || "waitlist"}`,
        ];
        const text = lines.join("\n");
        const html = `
          <div>
            <h2 style="margin:0 0 12px 0;">New ${requestType}</h2>
            <ul style="padding-left:16px;">
              <li><strong>Email:</strong> ${email}</li>
              <li><strong>Name:</strong> ${name || "-"}</li>
              <li><strong>Source:</strong> ${source || "waitlist"}</li>
            </ul>
          </div>
        `;

        console.log("[Waitlist API] Attempting to send email with:", {
          from: fromEmail,
          to: "<EMAIL>, <EMAIL>",
          subject,
        });

        const emailResult = await resend.emails.send({
          from: fromEmail,
          to: ["<EMAIL>", "<EMAIL>"],
          subject,
          text,
          html,
        });

        console.log("[Waitlist API] Email result:", emailResult);

        if (!emailResult || (emailResult as any)?.error) {
          throw new Error(
            `Failed to send email via Resend: ${JSON.stringify(emailResult)}`
          );
        }
      } else {
        console.error("[Waitlist API] Missing env vars:", {
          apiKey: !!apiKey,
          fromEmail: !!fromEmail,
        });
        return NextResponse.json(
          { success: false, error: "Email service not configured" },
          { status: 500 }
        );
      }
    } catch (emailError) {
      console.error(
        "[Waitlist API] Failed to send email via Resend:",
        emailError
      );
      return NextResponse.json(
        {
          success: false,
          error: `Failed to send waitlist email: ${emailError}`,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (e: any) {
    console.error("[Waitlist API] Unexpected error:", e);
    return NextResponse.json(
      { success: false, error: e?.message || "Unexpected error" },
      { status: 500 }
    );
  }
}
