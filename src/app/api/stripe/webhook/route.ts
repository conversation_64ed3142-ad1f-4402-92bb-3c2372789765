import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { headers } from "next/headers";
import { addCreditsToUser } from "@/lib/credits-api";

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = (await headers()).get("stripe-signature");

  if (!signature) {
    return NextResponse.json(
      { error: "No signature provided" },
      { status: 400 }
    );
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error("Webhook signature verification failed:", err);
    return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
  }

  // Handle the event
  switch (event.type) {
    case "payment_intent.succeeded":
      const paymentIntent = event.data.object;
      const { userId, credits, price } = paymentIntent.metadata;

      // 1. Verify payment was successful
      if (paymentIntent.status !== "succeeded") {
        console.log("Payment not succeeded, status:", paymentIntent.status);
        break;
      }

      // 2. Verify the amount matches expected price
      const expectedAmount = Math.round(parseFloat(price) * 100);
      if (paymentIntent.amount !== expectedAmount) {
        console.error(
          "Amount mismatch:",
          paymentIntent.amount,
          "expected:",
          expectedAmount
        );
        break;
      }

      // 3. Add credits to user account securely
      try {
        await addCreditsToUser(userId, parseInt(credits));
        console.log(
          `Payment succeeded for user ${userId}: ${credits} credits for $${price}`
        );
      } catch (error) {
        console.error("Failed to add credits to user:", error);
        // In production, you might want to retry or alert administrators
      }

      break;

    case "payment_intent.payment_failed":
      console.log("Payment failed");
      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
