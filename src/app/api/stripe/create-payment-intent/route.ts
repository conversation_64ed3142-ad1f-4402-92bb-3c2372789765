import { NextRequest, NextResponse } from "next/server";
import { stripe } from "@/lib/stripe";
import { auth } from "@clerk/nextjs/server";

// Server-side pricing validation
const CREDIT_PACKAGES = [
  { credits: 50, price: 9.99 },
  { credits: 100, price: 19.99 },
  { credits: 200, price: 39.99 },
  { credits: 500, price: 99.99 },
];

export async function POST(request: NextRequest) {
  try {
    // 1. Authenticate user
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await request.json();

    // 2. Validate package exists
    const selectedPackage = CREDIT_PACKAGES.find(
      (pkg) => pkg.credits === packageId
    );

    if (!selectedPackage) {
      return NextResponse.json(
        { error: "Invalid package selected" },
        { status: 400 }
      );
    }

    // 3. Create payment intent with server-validated pricing
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(selectedPackage.price * 100),
      currency: "usd",
      automatic_payment_methods: {
        enabled: true,
      },
      metadata: {
        userId: userId,
        credits: selectedPackage.credits.toString(),
        price: selectedPackage.price.toString(),
        type: "credit_purchase",
      },
    });

    console.log("Payment intent created:", paymentIntent);
    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
    });
  } catch (error) {
    console.error("Error creating payment intent:", error);
    return NextResponse.json(
      {
        error: "Failed to create payment intent",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
