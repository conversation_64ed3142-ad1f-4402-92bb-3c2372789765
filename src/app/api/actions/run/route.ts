import { NextRequest, NextResponse } from "next/server";
import { auth } from "@clerk/nextjs/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_ADMIN_API_URL ||
  process.env.NEXT_PUBLIC_API_URL ||
  "http://localhost:3002";

export async function POST(request: NextRequest) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    const resp = await fetch(`${BACKEND_URL}/api/actions/run`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!resp.ok) {
      const errorData = await resp.json().catch(() => ({}));
      console.error("Backend action run error:", {
        status: resp.status,
        statusText: resp.statusText,
        error: errorData,
      });
      return NextResponse.json(
        { success: false, error: errorData.message || "Backend request failed" },
        { status: resp.status }
      );
    }

    const data = await resp.json();
    return NextResponse.json({ success: true, data }, { status: 200 });
  } catch (e) {
    console.error("Error running action:", e);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
