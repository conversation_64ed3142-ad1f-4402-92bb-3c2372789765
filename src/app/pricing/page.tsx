"use client";

import { useState } from "react";
import { Check, Coins, CreditCard, Zap, Crown, Star } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreditPurchaseDialog } from "@/components/credits/credit-purchase-dialog";
import { useCreditsStore } from "@/stores/creditsStore";

const SUBSCRIPTION_PLANS = [
  {
    name: "Free",
    price: 0,
    period: "forever",
    description: "Perfect for trying out Siift",
    features: [
      "100 credits included",
      "Basic AI assistance",
      "Standard support",
      "Community access",
    ],
    icon: Star,
    popular: false,
    buttonText: "Current Plan",
    buttonVariant: "outline" as const,
  },
  {
    name: "Pro",
    price: 29,
    period: "month",
    description: "For professionals and small teams",
    features: [
      "500 credits per month",
      "Advanced AI features",
      "Priority support",
      "Team collaboration",
      "Custom integrations",
      "Analytics dashboard",
    ],
    icon: Zap,
    popular: true,
    buttonText: "Upgrade to Pro",
    buttonVariant: "default" as const,
  },
  {
    name: "Enterprise",
    price: 99,
    period: "month",
    description: "For large organizations",
    features: [
      "Unlimited credits",
      "All Pro features",
      "Dedicated support",
      "Custom AI models",
      "Advanced security",
      "SLA guarantee",
      "Custom training",
    ],
    icon: Crown,
    popular: false,
    buttonText: "Contact Sales",
    buttonVariant: "outline" as const,
  },
];

const CREDIT_PACKAGES = [
  { credits: 50, price: 9.99, popular: false },
  { credits: 100, price: 19.99, popular: true },
  { credits: 200, price: 39.99, popular: false },
  { credits: 500, price: 99.99, popular: false },
];

export default function PricingPage() {
  const { balance } = useCreditsStore();
  const [isCreditDialogOpen, setIsCreditDialogOpen] = useState(false);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-4xl font-bold tracking-tight text-foreground mb-4">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Choose the plan that works best for you. Upgrade or downgrade at any
            time.
          </p>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Coins className="h-4 w-4 text-[var(--primary)]" />
            <span>You currently have {balance} credits</span>
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="mt-16">
          <h2 className="text-2xl font-semibold text-center mb-8">
            Subscription Plans
          </h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {SUBSCRIPTION_PLANS.map((plan) => (
              <Card
                key={plan.name}
                className={`relative ${
                  plan.popular
                    ? "border-[var(--primary)] shadow-lg scale-105"
                    : "border-border"
                }`}
              >
                {plan.popular && (
                  <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-[var(--primary)] text-[var(--primary-foreground)]">
                    Most Popular
                  </Badge>
                )}
                <CardHeader className="text-center pb-4">
                  <div className="mx-auto mb-4 p-3 rounded-full bg-[var(--primary)]/10 w-fit">
                    <plan.icon className="h-6 w-6 text-[var(--primary)]" />
                  </div>
                  <CardTitle className="text-2xl">{plan.name}</CardTitle>
                  <CardDescription className="text-base">
                    {plan.description}
                  </CardDescription>
                  <div className="mt-4">
                    <span className="text-4xl font-bold text-foreground">
                      ${plan.price}
                    </span>
                    <span className="text-muted-foreground">
                      /{plan.period}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-3">
                        <Check className="h-4 w-4 text-[var(--primary)] flex-shrink-0" />
                        <span className="text-sm text-foreground">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                  <Button
                    className="w-full"
                    variant={plan.buttonVariant}
                    disabled={plan.name === "Free"}
                  >
                    {plan.buttonText}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Credit Packages */}
        <div className="mt-20">
          <h2 className="text-2xl font-semibold text-center mb-8">
            Buy Credits
          </h2>
          <p className="text-center text-muted-foreground mb-8 max-w-2xl mx-auto">
            Need more credits? Purchase them individually. Credits never expire
            and can be used for any AI feature.
          </p>
          <div className="grid md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {CREDIT_PACKAGES.map((pkg) => (
              <Card
                key={pkg.credits}
                className={`relative cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  pkg.popular
                    ? "border-[var(--primary)] shadow-lg"
                    : "border-border hover:border-[var(--primary)]/50"
                }`}
                onClick={() => setIsCreditDialogOpen(true)}
              >
                {pkg.popular && (
                  <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[var(--primary)] text-[var(--primary-foreground)]">
                    Best Value
                  </Badge>
                )}
                <CardHeader className="text-center pb-4">
                  <div className="text-3xl font-bold text-foreground mb-2">
                    {pkg.credits}
                  </div>
                  <div className="text-sm text-muted-foreground mb-4">
                    credits
                  </div>
                  <div className="text-2xl font-semibold text-[var(--primary)]">
                    ${pkg.price}
                  </div>
                </CardHeader>
                <CardContent className="text-center">
                  <Button
                    className="w-full"
                    variant={pkg.popular ? "default" : "outline"}
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Purchase
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-2xl font-semibold text-center mb-8">
            Frequently Asked Questions
          </h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="border border-border rounded-lg p-6">
              <h3 className="font-semibold text-foreground mb-2">
                How do credits work?
              </h3>
              <p className="text-muted-foreground">
                Credits are used for AI-powered features. Each action consumes a
                certain number of credits based on complexity. Credits never
                expire and can be used at any time.
              </p>
            </div>
            <div className="border border-border rounded-lg p-6">
              <h3 className="font-semibold text-foreground mb-2">
                Can I change my plan anytime?
              </h3>
              <p className="text-muted-foreground">
                Yes! You can upgrade or downgrade your subscription at any time.
                Changes take effect immediately, and we'll prorate any billing
                differences.
              </p>
            </div>
            <div className="border border-border rounded-lg p-6">
              <h3 className="font-semibold text-foreground mb-2">
                What payment methods do you accept?
              </h3>
              <p className="text-muted-foreground">
                We accept all major credit cards, debit cards, and PayPal. All
                payments are processed securely through Stripe.
              </p>
            </div>
            <div className="border border-border rounded-lg p-6">
              <h3 className="font-semibold text-foreground mb-2">
                Is there a free trial?
              </h3>
              <p className="text-muted-foreground">
                Yes! The Free plan includes 100 credits to get you started. You
                can also purchase additional credits anytime without a
                subscription.
              </p>
            </div>
          </div>
        </div>
      </div>

      <CreditPurchaseDialog
        isOpen={isCreditDialogOpen}
        onClose={() => setIsCreditDialogOpen(false)}
      />
    </div>
  );
}
