import type { BusinessItem, BusinessItemDetail } from "@/types/BusinessSection.types";
import { computeEntryCounts } from "@/utils/entryCounts";

// Helper function to safely normalize any value to a short display string
const toText = (value: unknown): string => {
  if (value == null) return "";
  if (typeof value === "string") return value;
  if (typeof value === "number" || typeof value === "boolean")
    return String(value);
  if (Array.isArray(value))
    return value
      .map((v) => toText(v))
      .filter(Boolean)
      .join(" ");
  if (typeof value === "object") {
    const v: any = value;
    const candidate = [
      v.title,
      v.idea,
      v.action,
      v.result,
      v.text,
      v.content,
    ].find((x) => typeof x === "string" && x.trim().length > 0);
    return candidate ? String(candidate) : "";
  }
  return "";
};

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return "AlertTriangle";
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return "Users";
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return "Lightbulb";
  if (titleLower.includes("market") || titleLower.includes("size"))
    return "BarChart3";
  if (titleLower.includes("trend")) return "TrendingUp";
  if (titleLower.includes("value") || titleLower.includes("uvp")) return "Star";
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return "Package";
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return "Settings";
  if (titleLower.includes("positioning")) return "Target";
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return "Megaphone";
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return "Palette";
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return "MessageCircle";
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return "FileText";
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return "Building";
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return "DollarSign";
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return "Shield";
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return "Heart";
  return "Lightbulb"; // Default fallback
};

/**
 * Centralized function to load topic details from Zustand store
 * This ensures consistent data loading regardless of which component calls it
 */
export function loadTopicDetails(params: {
  sectionId: string;
  numericTopicId: number;
  topicId?: string;
  topicTitle: string;
  topicIcon?: any;
  allTopics?: any[];
  getTopicEntries: (sectionId: string, topicId: string) => any[];
  setSelectedItem: (item: BusinessItem | null) => void;
  setItemDetails: (details: BusinessItemDetail[]) => void;
}) {
  const {
    sectionId,
    numericTopicId,
    topicId,
    topicTitle,
    topicIcon,
    allTopics = [],
    getTopicEntries,
    setSelectedItem,
    setItemDetails,
  } = params;

  console.log('[TopicLoader] loadTopicDetails', {
    sectionId,
    numericTopicId,
    topicId,
    topicTitle
  });

  // Get all entries for this topic from store (single source of truth)
  const allTopicEntries = getTopicEntries(sectionId, String(numericTopicId)) as any[];
  
  console.log('[TopicLoader] Found entries:', {
    entriesCount: allTopicEntries.length,
    entries: allTopicEntries.slice(0, 2) // Show first 2 for debugging
  });

  // Convert to BusinessItemDetail format
  const itemDetails: BusinessItemDetail[] = allTopicEntries.map((e: any) => ({
    id: String(e.id),
    title:
      toText(e.title) ||
      toText(e.action) ||
      toText(e.idea) ||
      toText(e.result),
    actions: toText(e.actions) || toText(e.action),
    result: toText(e.result),
    status: e.status || "idea",
    description: e.description,
    createdAt: e.createdAt,
    updatedAt: e.updatedAt,
    metadata: e.metadata,
  }));

  // Calculate counts for the topic
  const counts = computeEntryCounts(allTopicEntries);

  // Find topic dependencies and unlock status from allTopics if available
  const topicData = allTopics.find(
    (t: any) => 
      t.numericTopicId === numericTopicId && 
      (t.mappedCategoryId ?? t.sectionId) === sectionId
  );

  const dependencies = topicData?.dependencies || [];
  const dependencyLabels = topicData?.dependencyLabels || [];
  
  // For now, assume topics are unlocked (dependency calculation can be added later)
  const isUnlocked = true;
  const lockedDependencies: string[] = [];

  // Create a BusinessItem from this topic
  const businessItem: BusinessItem & {
    sectionId?: string;
    topicId?: string;
    numericTopicId?: number;
    isUnlocked?: boolean;
    lockedDependencies?: string[];
  } = {
    id: String(numericTopicId),
    title: topicTitle,
    status: counts.confirmed > 0 ? "confirmed" : counts.actions > 0 ? "action" : "idea",
    actions: counts.actions,
    ideas: counts.ideas,
    results: counts.confirmed,
    icon: topicIcon || getTopicIcon(topicTitle),
    sectionId,
    topicId: topicId || String(numericTopicId),
    numericTopicId,
    isUnlocked,
    lockedDependencies,
  };

  console.log('[TopicLoader] Setting selected item:', {
    id: businessItem.id,
    title: businessItem.title,
    status: businessItem.status,
    counts: {
      ideas: businessItem.ideas,
      actions: businessItem.actions,
      results: businessItem.results,
    },
    entriesCount: itemDetails.length,
  });

  // Update the store
  setSelectedItem(businessItem);
  setItemDetails(itemDetails);

  return {
    businessItem,
    itemDetails,
    counts,
  };
}
