"use client";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { Globe } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useMemo, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>etContent,
  <PERSON>etHeader,
  SheetTitle,
} from "@/components/ui/sheet";

export function DragHandle({ detail }: { detail: BusinessItemDetail }) {
  const [open, setOpen] = useState(false);

  const research = useMemo(() => {
    try {
      const raw = (detail as any)?.metadata;
      const meta = typeof raw === "string" ? JSON.parse(raw) : raw;
      return meta?.research || null;
    } catch {
      return null;
    }
  }, [detail]);

  const facts: string[] = useMemo(() => {
    const list = (
      research && Array.isArray(research.facts) ? research.facts : []
    ) as string[];
    return list
      .filter((f) => typeof f === "string" && f.trim().length > 0)
      .slice(0, 10);
  }, [research]);

  const sources: string[] = useMemo(() => {
    const list = (
      research && Array.isArray(research.sources) ? research.sources : []
    ) as string[];
    return list
      .filter((u) => typeof u === "string" && u.trim().length > 0)
      .slice(0, 10);
  }, [research]);

  const summary: string | null = useMemo(() => {
    const s = (
      research && typeof research.summary === "string" ? research.summary : ""
    ) as string;
    return s?.trim() ? s.trim() : null;
  }, [research]);

  const hasCitations =
    Boolean(research) &&
    (facts.length > 0 || summary || (sources && sources.length > 0));

  return (
    <div className="py-3 px-3 flex items-center justify-center  transition-all duration-200">
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            type="button"
            onClick={() => setOpen(true)}
            className={`inline-flex items-center justify-center rounded p-1.5 text-[10px] border  ${
              hasCitations
                ? "bg-[var(--input)]/10 border-[var(--input)] hover:bg-[var(--accent)]/10"
                : "bg-[var(--background)]/70 border-[var(--siift-light-mid)]/60"
            }`}
            aria-label="View AI research"
          >
            <Globe
              className={`h-3 w-3 ${
                hasCitations
                  ? "text-[var(--muted-foreground)]"
                  : "text-[var(--input)]"
              }`}
            />
          </button>
        </TooltipTrigger>
        <TooltipContent side="right" align="center">
          <div className="text-xs">
            {hasCitations
              ? "View citations and sources"
              : "No citations available"}
          </div>
        </TooltipContent>
      </Tooltip>

      <Sheet open={open} onOpenChange={setOpen}>
        <SheetContent side="right" className="w-[520px] max-w-[90vw] p-0">
          <SheetHeader>
            <SheetTitle className="text-md">AI Research</SheetTitle>
          </SheetHeader>
          <div className="space-y-4 mt-2 px-4">
            {summary && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3 text-sm">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-1">
                  Summary
                </div>
                <div className="leading-relaxed text-[var(--foreground)]">
                  {summary}
                </div>
              </div>
            )}

            {facts.length > 0 && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-2">
                  Validations
                </div>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  {facts.map((f, i) => (
                    <li key={i} className="leading-snug">
                      {f}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {sources.length > 0 && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-2">
                  Sources
                </div>
                <ul className="space-y-1 text-xs">
                  {sources.map((u, i) => (
                    <li key={i} className="truncate">
                      <a
                        href={u}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline text-[var(--primary)] hover:opacity-80 break-all"
                      >
                        {u}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {!summary && facts.length === 0 && sources.length === 0 && (
              <div className="mt-2 text-sm text-muted-foreground">
                No research data found for this row.
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
