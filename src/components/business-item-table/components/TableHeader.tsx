"use client";

import { TableH<PERSON>, TableHeader, TableRow } from "@/components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Info } from "lucide-react";

export function BusinessItemsTableHeader() {
  return (
    <TableHeader className="sticky top-0 bg-[var(--background)] backdrop-blur-sm z-50 border-b-3  border-[var(--siift-light-mid)]/50 ">
      <TableRow className="border-b-0">
        {/* <TableHead className="bg-[var(--background)] border-r  border-[var(--siift-light-mid)]/50 "></TableHead> */}
        <TableHead className="font-semibold text-lg border-r  border-[var(--siift-light-mid)]/50  bg-[var(--background)]">
          <div className="flex items-center gap-2">
            <span>Idea</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="text-[var(--muted-foreground)] hover:text-[var(--foreground)] inline-flex items-center justify-center p-1 rounded-sm hover:bg-[var(--siift-light-main)] transition-colors"
                  aria-label="What is an Idea?"
                >
                  <Info className="w-3 h-3" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="start" className="max-w-xs">
                <p className="text-xs">What is the main idea of the item?</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </TableHead>
        <TableHead className="font-semibold text-lg border-r  border-[var(--siift-light-mid)]/50  bg-[var(--background)]">
          <div className="flex items-center gap-2">
            <span>Action</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="text-[var(--muted-foreground)] hover:text-[var(--foreground)] inline-flex items-center justify-center p-1 rounded-sm hover:bg-[var(--siift-light-main)] transition-colors"
                  aria-label="What are Actions?"
                >
                  <Info className="w-3 h-3" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="start" className="max-w-xs">
                <p className="text-xs">What was done to achieve the idea?</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </TableHead>
        <TableHead className="font-semibold text-lg border-r  border-[var(--siift-light-mid)]/50  bg-[var(--background)]">
          <div className="flex items-center gap-2">
            <span>Results</span>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="text-[var(--muted-foreground)] hover:text-[var(--foreground)] inline-flex items-center justify-center p-1 rounded-sm hover:bg-[var(--siift-light-main)] transition-colors"
                  aria-label="What are Actions?"
                >
                  <Info className="w-3 h-3" />
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="start" className="max-w-xs">
                <p className="text-xs">What was the result of the action?</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </TableHead>
      </TableRow>
    </TableHeader>
  );
}

export default BusinessItemsTableHeader;
