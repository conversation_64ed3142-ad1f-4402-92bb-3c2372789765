"use client";

import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { Check, Lightbulb, Zap } from "lucide-react";
import {
  getAutoStatus,
  getStatusColor,
  isStatusEditable,
} from "../utils/status";

export function StatusSelect({
  value,
  onChange,
  placeholder,
  disabled = false,
  detail,
  isItemLocked = false,
}: {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  detail: BusinessItemDetail;
  isItemLocked?: boolean;
}) {
  const editable = isStatusEditable(
    detail.title,
    detail.actions,
    detail.result
  );

  if (!editable) {
    // Show as non-editable badge with icon
    const autoStatus = getAutoStatus(
      detail.title,
      detail.actions,
      detail.result
    );

    // Don't show any status if there's no idea (title)
    if (!detail.title.trim()) {
      return null;
    }

    const getStatusIcon = (status: string) => {
      switch (status) {
        case "idea":
          return <Lightbulb className="h-3 w-3" />;
        case "action":
          return <Zap className="h-3 w-3" />;
        case "confirmed":
          return <Check className="h-3 w-3" />;
        default:
          return null;
      }
    };

    return (
      <Badge
        className={`${getStatusColor(
          autoStatus
        )} capitalize text-xs px-1.5 py-0.5 h-6`}
      >
        {getStatusIcon(autoStatus)}
        {autoStatus}
      </Badge>
    );
  }

  return (
    <Select value={value} onValueChange={onChange} disabled={disabled}>
      <SelectTrigger className="w-[120px] h-7 px-2 ">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="confirmed" disabled={false}>
          <Badge
            className={`bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 border border-green-700 dark:border-green-600 text-xs px-1.5 py-0.5 h-6 ${
              isItemLocked ? "opacity-50" : ""
            }`}
          >
            Validated
          </Badge>
        </SelectItem>
        <SelectItem value="unproven" disabled={false}>
          <Badge
            className={`bg-red-100 dark:bg-red-900 text-red-900 dark:text-red-200 border border-red-700 dark:border-red-600 text-xs px-1.5 py-0.5 h-6 ${
              isItemLocked ? "opacity-50" : ""
            }`}
          >
            Invalidated
          </Badge>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
