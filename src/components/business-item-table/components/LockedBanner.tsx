"use client";

import { Lock, Info } from "lucide-react";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import type { ProjectTopicSummary } from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";

export interface LockedBannerProps {
  isItemLocked: boolean;
  lockedDependencies: any[];
  projectId: string;
  selectedBusinessItem: BusinessItem | null;
  sections: Array<any> | undefined;
  getTopicEntries: (sectionId: string, topicId: string) => BusinessItemDetail[];
  setTopicEntries: (
    sectionId: string,
    topicId: string,
    entries: BusinessItemDetail[]
  ) => void;
  setSelectedItem: (item: any) => void;
  allTopics: ProjectTopicSummary[];
  stage?: string;
}

export function LockedBanner({
  isItemLocked,
  lockedDependencies,
  projectId,
  selectedBusinessItem,
  sections,
  getTopicEntries,
  setTopicEntries,
  setSelectedItem,
  allTopics,
}: LockedBannerProps) {
  const { findTopicItemById } = useBusinessSectionStore();
  if (!isItemLocked) return null;

  // Use same label normalization as BusinessSectionsGrid to match display titles
  const normalizeLabel = (label: string) => {
    const base = (label || "")
      .toString()
      .trim()
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, " ")
      .replace(/\s+/g, " ")
      .trim();

    const singular =
      base.endsWith("s") && base.length > 3 ? base.slice(0, -1) : base;

    const synonyms: Record<string, string> = {
      uvp: "value proposition",
      "unique value proposition": "value proposition",
      customers: "customer",
      audience: "customer",
      competitors: "competitor",
      alternatives: "competitor",
      "market sizing": "market",
      "market size": "market",
      tech: "technology",
    };

    return synonyms[singular] || singular;
  };

  const resolveCanonical = (
    topicSummary?: ProjectTopicSummary,
    fallbackLabel?: string
  ) => {
    try {
      const sectionId = String(
        (topicSummary as any)?.mappedCategoryId ??
          (topicSummary as any)?.sectionId ??
          ""
      );
      const labelKey = normalizeLabel(
        (topicSummary as any)?.mappedLabel ||
          (topicSummary as any)?.title ||
          String(fallbackLabel || "")
      );
      if (!labelKey) return null;
      const candidates = (allTopics as ProjectTopicSummary[]).filter((t) => {
        const tSection = String(t.mappedCategoryId ?? t.sectionId ?? "");
        if (sectionId && tSection !== sectionId) return false;
        return normalizeLabel(t.mappedLabel || t.title) === labelKey;
      });
      if (!candidates.length) return null;
      const canonicalNumericId = Math.min(
        ...candidates.map((c) => c.numericTopicId)
      );
      const canonical =
        candidates.find((c) => c.numericTopicId === canonicalNumericId) ||
        candidates[0];
      return {
        canonicalId: String(canonical.numericTopicId),
        displayTitle: canonical.mappedLabel || canonical.title,
        sectionId: String(canonical.mappedCategoryId ?? canonical.sectionId),
        canonicalTopic: canonical,
      } as const;
    } catch {
      return null;
    }
  };

  // Note: layerFilledMap computation moved to dependency logic below

  return (
    <div className="">
      <div className="bg-[var(--destructive)]/10 dark:bg-[var(--siift-dark-main)]  p-4 border-b-2 border-[var(--primary-foreground)]">
        <div className="flex items-start gap-3">
          <Lock className="h-5 w-5  mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-semibold text-[var(--siift-darkest)] dark:text-[var(--siift-lightest)] mb-1">
              This topic is locked for now
            </h3>
            <p className="text-sm text-[var(--siift-dark-main)] dark:text-[var(--siift-light-main)] mb-2">
              you can add ideas but actions are disabled until you complete:
            </p>
            {lockedDependencies.length > 0 && (
              <div>
                <p className="text-sm font-medium text-[var(--siift-dark-main)] dark:text-[var(--siift-light-main)] mb-1">
                  Complete these topics first:
                </p>
                <ul className="text-sm text-[var(--siift-dark-main)] dark:text-[var(--siift-light-main)] list-disc list-inside space-y-1">
                  {lockedDependencies.map((dep: any, index: number) => {
                    return (
                      <li key={index}>
                        <button
                          className="text-[var(--primary)] hover:text-[var(--hover-primary)] underline"
                          onClick={() => {
                            try {
                              const topicSummary =
                                (dep?.topic as
                                  | ProjectTopicSummary
                                  | undefined) || undefined;
                              const rawLabel =
                                typeof dep === "string"
                                  ? dep
                                  : (dep?.label as string) ||
                                    topicSummary?.mappedLabel ||
                                    topicSummary?.title ||
                                    "";
                              const resolved = resolveCanonical(
                                topicSummary,
                                rawLabel
                              );
                              const canonicalId = String(
                                resolved?.canonicalId ||
                                  (topicSummary as any)?.numericTopicId ||
                                  (dep as any)?.id ||
                                  ""
                              );
                              const chosenSectionId: string = String(
                                resolved?.sectionId ||
                                  (topicSummary as any)?.mappedCategoryId ||
                                  (topicSummary as any)?.sectionId ||
                                  ""
                              );
                              const found = canonicalId
                                ? findTopicItemById(canonicalId)
                                : null;
                              let existingEntries: BusinessItemDetail[] = [];
                              if (chosenSectionId && canonicalId) {
                                const entries = (getTopicEntries(
                                  chosenSectionId,
                                  canonicalId
                                ) || []) as BusinessItemDetail[];
                                if (
                                  Array.isArray(entries) &&
                                  entries.length > 0
                                ) {
                                  existingEntries = entries;
                                }
                              }

                              const layerFilledMap = new Map<string, boolean>();
                              try {
                                (allTopics as ProjectTopicSummary[]).forEach(
                                  (t) => {
                                    const sectionKey =
                                      t.mappedCategoryId ?? t.sectionId;
                                    const entries = (getTopicEntries(
                                      String(sectionKey),
                                      String(t.topicId)
                                    ) || []) as BusinessItemDetail[];
                                    const anyProgress = entries.some(
                                      (e) =>
                                        e.status === "action" ||
                                        e.status === "unproven" ||
                                        e.status === "confirmed"
                                    );
                                    if (t.layer)
                                      layerFilledMap.set(t.layer, anyProgress);
                                  }
                                );
                              } catch {}

                              const baseTopic: any =
                                (resolved?.canonicalTopic as any) ||
                                (topicSummary as any) ||
                                {};
                              const depsArr: string[] = Array.isArray(
                                baseTopic?.dependencies
                              )
                                ? (baseTopic.dependencies as string[])
                                : [];
                              const missingDeps = depsArr.filter(
                                (d) => layerFilledMap.get(d) !== true
                              );
                              // 🚀 Use dependency service for unlocking (replaces hardcoded stage logic)
                              // Unlocking logic: Topics unlock when all their dependencies show progress
                              const isUnlocked = depsArr.length === 0 ? true : missingDeps.length === 0;
                              const lockedDependencyObjects = !isUnlocked
                                ? missingDeps.map((d) => {
                                    const idx = depsArr.indexOf(d);
                                    const label =
                                      ((Array.isArray(
                                        baseTopic?.dependencyLabels
                                      )
                                        ? baseTopic.dependencyLabels
                                        : [])[idx] as string) || d;
                                    const dependencyTopic = (
                                      allTopics as ProjectTopicSummary[]
                                    ).find(
                                      (topic) =>
                                        topic.layer === d ||
                                        String(topic.topicId) === String(d)
                                    );
                                    return {
                                      id: d,
                                      label,
                                      topic: dependencyTopic,
                                    } as any;
                                  })
                                : [];

                              const foundItem =
                                found?.item ||
                                sections
                                  ?.find((s) => s.id === chosenSectionId)
                                  ?.items.find(
                                    (it: any) =>
                                      String(it.id) === String(canonicalId)
                                  );

                              if (foundItem) {
                                setSelectedItem({
                                  ...(foundItem as any),
                                  sectionId: chosenSectionId,
                                  isUnlocked,
                                  lockedDependencies: lockedDependencyObjects,
                                } as any);
                              } else {
                                const actionsCount = existingEntries.filter(
                                  (e) => e.status === "action"
                                ).length;
                                const ideasCount = existingEntries.filter(
                                  (e) => e.status === "idea"
                                ).length;
                                const resultsCount = existingEntries.filter(
                                  (e) => e.status === "confirmed"
                                ).length;
                                const derivedStatus =
                                  (existingEntries[0]?.status as any) ||
                                  (selectedBusinessItem as any)?.status ||
                                  "idea";

                                const nextItem = {
                                  id: canonicalId,
                                  title:
                                    resolved?.displayTitle ||
                                    topicSummary?.mappedLabel ||
                                    topicSummary?.title ||
                                    (typeof dep === "string"
                                      ? dep
                                      : dep?.label) ||
                                    `Topic ${canonicalId}`,
                                  status: derivedStatus,
                                  actions: actionsCount,
                                  ideas: ideasCount,
                                  results: resultsCount,
                                  icon: Info,
                                  sectionId: chosenSectionId,
                                  entries: existingEntries,
                                  isUnlocked,
                                  lockedDependencies: lockedDependencyObjects,
                                } as unknown as BusinessItem & {
                                  entries?: BusinessItemDetail[];
                                  sectionId?: string;
                                  isUnlocked?: boolean;
                                  lockedDependencies?: string[];
                                };

                                setSelectedItem(nextItem);
                              }

                              if (
                                !existingEntries.length &&
                                chosenSectionId &&
                                canonicalId
                              ) {
                                (async () => {
                                  try {
                                    const res = await fetch(
                                      `/api/projects/${projectId}/topics/${canonicalId}/entries`,
                                      { cache: "no-store" }
                                    );
                                    if (!res.ok) return;
                                    const json = await res
                                      .json()
                                      .catch(() => ({}));
                                    const payload = (json?.data ?? json) as any;
                                    const entriesArr = Array.isArray(payload)
                                      ? payload
                                      : Array.isArray(payload?.entries)
                                      ? payload.entries
                                      : Array.isArray(payload?.data)
                                      ? payload.data
                                      : [];
                                    const mapped: BusinessItemDetail[] = (
                                      entriesArr || []
                                    ).map((e: any) => ({
                                      id: String(e.id),
                                      title: e.idea || "",
                                      actions: e.action || "",
                                      result: e.result || "",
                                      status: (e.status as any) || "idea",
                                      updatedAt:
                                        (e as any).updatedAt ||
                                        (e as any).updated_at ||
                                        undefined,
                                      createdAt:
                                        (e as any).createdAt ||
                                        (e as any).created_at ||
                                        undefined,
                                    }));
                                    setTopicEntries(
                                      chosenSectionId,
                                      canonicalId,
                                      mapped
                                    );
                                  } catch (err) {
                                    console.error(
                                      "❌ [LockedBanner] fetch dependency entries error",
                                      err
                                    );
                                  }
                                })();
                              }
                            } catch (e) {
                              console.error(
                                "❌ [LockedBanner] Failed to navigate to dependency topic",
                                e
                              );
                            }
                          }}
                        >
                          {(() => {
                            try {
                              const topicSummary =
                                (dep?.topic as
                                  | ProjectTopicSummary
                                  | undefined) || undefined;
                              const rawLabel =
                                typeof dep === "string"
                                  ? dep
                                  : (dep?.label as string) ||
                                    topicSummary?.mappedLabel ||
                                    topicSummary?.title ||
                                    "";
                              const resolved = resolveCanonical(
                                topicSummary,
                                rawLabel
                              );
                              return (
                                resolved?.displayTitle ||
                                topicSummary?.mappedLabel ||
                                topicSummary?.title ||
                                rawLabel ||
                                (dep as any)?.id ||
                                (dep as any)?.title ||
                                "Dependency"
                              );
                            } catch {
                              return typeof dep === "string"
                                ? dep
                                : dep?.label ||
                                    (dep as any)?.title ||
                                    "Dependency";
                            }
                          })()}
                        </button>
                        {/* Dependency status is shown only after navigation in the target topic view */}
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default LockedBanner;
