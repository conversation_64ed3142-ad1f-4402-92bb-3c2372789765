"use client";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { EditableCell } from "../../EditableCell";
import { useState, useMemo } from "react";
import { Beaker } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface IdeaCellProps {
  detail: BusinessItemDetail;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
}

export function IdeaCell({
  detail,
  editingCell,
  setEditingCell,
  onSave,
}: IdeaCellProps) {
  const isEditingTitle =
    editingCell?.id === detail.id && editingCell?.field === "title";
  const [isResearchOpen, setIsResearchOpen] = useState(false);

  const research = useMemo(() => {
    try {
      const raw = (detail as any)?.metadata;
      const meta = typeof raw === "string" ? JSON.parse(raw) : raw;
      return meta?.research || null;
    } catch {
      return null;
    }
  }, [detail]);

  const facts: string[] = useMemo(() => {
    const list = (
      research && Array.isArray(research.facts) ? research.facts : []
    ) as string[];
    return list
      .filter((f) => typeof f === "string" && f.trim().length > 0)
      .slice(0, 10);
  }, [research]);

  const sources: string[] = useMemo(() => {
    const list = (
      research && Array.isArray(research.sources) ? research.sources : []
    ) as string[];
    return list
      .filter((u) => typeof u === "string" && u.trim().length > 0)
      .slice(0, 10);
  }, [research]);

  const summary: string | null = useMemo(() => {
    const s = (
      research && typeof research.summary === "string" ? research.summary : ""
    ) as string;
    return s?.trim() ? s.trim() : null;
  }, [research]);

  return (
    <div className="flex flex-col justify-between h-full ">
      <div
        className={`flex-1 p-2 border relative ${
          isEditingTitle
            ? "border-[var(--ring)] border-2 "
            : "border-transparent"
        }`}
      >
        <EditableCell
          id={detail.id}
          field="title"
          value={detail.title}
          multiline={true}
          className="h-full"
          fillContainer={true}
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
        />

        {/* {research &&
          (facts.length > 0 || summary || (sources && sources.length > 0)) && (
            <div className="absolute top-1.5 right-2 flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    type="button"
                    onClick={() => setIsResearchOpen(true)}
                    className="inline-flex items-center gap-1 rounded px-1.5 py-0.5 text-[10px] border border-[var(--siift-light-mid)]/60 bg-[var(--background)]/70 hover:bg-[var(--accent)]/10 hover:border-[var(--accent)] transition-colors"
                    aria-label="View AI research"
                  >
                    <Beaker className="h-3 w-3" />
                    <span className="leading-none">research</span>
                    {facts.length > 0 && (
                      <span className="ml-1 rounded-sm px-1 text-[9px] bg-[var(--muted)] text-[var(--muted-foreground)]">
                        {facts.length}
                      </span>
                    )}
                  </button>
                </TooltipTrigger>
                <TooltipContent side="left" align="end">
                  <div className="text-xs">
                    View validations{facts.length ? ` (${facts.length})` : ""}{" "}
                    and sources
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
          )} */}
      </div>

      <div className="border-t border-[var(--siift-light-mid)]/50">
        {(() => {
          // Compute average progress across all actions for this entry
          const parseActions = (
            actions: string
          ): Array<{
            progress?: number;
            results?: Array<{ content?: string }>;
          }> => {
            if (!actions || typeof actions !== "string") return [];
            try {
              const parsed = JSON.parse(actions as string);
              const items = Array.isArray(parsed)
                ? parsed
                : Array.isArray((parsed as any)?.actions)
                ? (parsed as any).actions
                : [];
              return Array.isArray(items) ? items : [];
            } catch {
              return [];
            }
          };

          const items = parseActions(detail.actions || "");
          // Only count actions that have a non-empty result; others contribute 0
          const progressValues = items.map((a) => {
            const hasNonEmptyResult = Boolean(
              (a?.results?.[0]?.content || "").trim()
            );
            const p = Number(a?.progress) || 0;
            return hasNonEmptyResult ? p : 0;
          });
          const avg =
            progressValues.length > 0
              ? Math.round(
                  progressValues.reduce((s, v) => s + v, 0) /
                    progressValues.length
                )
              : 0;
          return (
            <div className="w-full flex items-center gap-4 p-2">
              <div className="text-xs text-[var(--text-muted)]/50 whitespace-nowrap">
                Validation
              </div>
              <div className="w-full h-2 rounded-sm border border-[var(--progress-border)]/30 bg-[var(--progress-bg)]/30 relative overflow-hidden">
                {/* Progress fill with striped pattern */}
                <div
                  className="h-full rounded-sm bg-[var(--progress-fill)] transition-all duration-300 relative"
                  style={{
                    width: `${avg}%`,
                  }}
                ></div>
                <div
                  className="absolute inset-0 opacity-30"
                  style={{
                    backgroundImage: `repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 1.5px,
                    rgba(156, 163, 175, 0.4) 1.5px,
                    rgba(156, 163, 175, 0.4) 3px
                  )`,
                  }}
                />
              </div>
            </div>
          );
        })()}
      </div>

      {/* Research Details Sheet */}
      <Sheet open={isResearchOpen} onOpenChange={setIsResearchOpen}>
        <SheetContent side="right" className="w-[520px] max-w-[90vw] p-4">
          <SheetHeader>
            <SheetTitle className="text-md">AI Research</SheetTitle>
          </SheetHeader>

          <div className="space-y-4 mt-2">
            {summary && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3 text-sm">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-1">
                  Summary
                </div>
                <div className="leading-relaxed text-[var(--foreground)]">
                  {summary}
                </div>
              </div>
            )}

            {facts.length > 0 && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-2">
                  Validations
                </div>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  {facts.map((f, i) => (
                    <li key={i} className="leading-snug">
                      {f}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {sources.length > 0 && (
              <div className="rounded-md border border-[var(--siift-light-mid)]/40 bg-[var(--background)]/70 p-3">
                <div className="text-xs uppercase tracking-wide text-[var(--muted-foreground)] mb-2">
                  Sources
                </div>
                <ul className="space-y-1 text-xs">
                  {sources.map((u, i) => (
                    <li key={i} className="truncate">
                      <a
                        href={u}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline text-[var(--primary)] hover:opacity-80 break-all"
                      >
                        {u}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
