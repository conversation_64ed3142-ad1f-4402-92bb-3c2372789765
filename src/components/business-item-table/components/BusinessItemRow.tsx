"use client";

import { TableCell, TableRow } from "@/components/ui/table";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { Draggable } from "@hello-pangea/dnd";

import { DragHandle } from "./DragHandle";
import { ActionsCell } from "./ActionsCell";
import { SuggestionsCell } from "./SuggestionsCell";

import { IdeaCell } from "./IdeaCell";

export function BusinessItemRow({
  detail,
  index,
  editingCell,
  setEditingCell,
  onSave,
  isItemLocked = false,
}: {
  detail: BusinessItemDetail;
  index: number;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  isItemLocked?: boolean;
}) {
  return (
    <Draggable
      key={String(detail.id)}
      draggableId={String(detail.id)}
      index={index}
      isDragDisabled={true}
    >
      {(provided, snapshot) => (
        <TableRow
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`group hover:bg-gray-100 dark:hover:bg-[var(--background)]/20 transition-all duration-200 border-b-3 border-[var(--siift-light-mid)]/50  relative z-20 min-h-32 ${
            snapshot.isDragging
              ? "bg-gray-200 dark:bg-gray-700 shadow-lg scale-[1.02] rotate-1 z-30"
              : ""
          } ${
            index % 2 === 1 ? "bg-[var(--siift-light-mid)]/15" : "bg-background"
          }`}
        >
          {/* <TableCell className="py-0 px-0 border-r  border-[var(--siift-light-mid)]/50  w-12">
            <DragHandle detail={detail} />
          </TableCell> */}
          <TableCell className="h-16 relative border-r p-0  border-[var(--siift-light-mid)]/50  align-top">
            <div className="absolute inset-0 " />
            <IdeaCell
              detail={detail}
              editingCell={editingCell}
              setEditingCell={setEditingCell}
              onSave={onSave}
            />
          </TableCell>
          <TableCell className="border-r  border-[var(--siift-light-mid)]/50  align-top whitespace-normal break-words">
            <SuggestionsCell
              detail={detail}
              disabled={isItemLocked}
              onSave={onSave}
            />
          </TableCell>
          <TableCell className="border-r  border-[var(--siift-light-mid)]/50  align-top whitespace-normal break-words">
            <ActionsCell
              detail={detail}
              disabled={isItemLocked}
              onSave={onSave}
            />
          </TableCell>
        </TableRow>
      )}
    </Draggable>
  );
}
