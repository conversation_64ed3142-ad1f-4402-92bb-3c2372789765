"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { StatusCountBadge } from "@/components/ui/status-count-badge";
import { useProjects, useProjectsLimited } from "@/hooks/queries/useProjects";
import {
  useAllProjectTopics,
  useAllTopicEntries,
} from "@/hooks/queries/useTopics";
import { computeEntryCounts } from "@/utils/entryCounts";
import { Clock, Trash } from "lucide-react";
import { useRouter } from "next/navigation";
import { formatRelativeTime } from "../../lib/utils";
import { Logo } from "../ui/logo";
import { Tooltip, TooltipTrigger } from "../ui/tooltip";
import { IButton } from "../ui/ibutton";
import { authApi } from "@/lib/api";
import { invalidateQueries } from "@/lib/queryClient";
import { DASHBOARD_CONFIG } from "@/lib/constants";

/**
 * Dashboard Projects Section Component
 * 
 * Displays a limited list of recent projects with optional controls.
 * Optimized for dashboard use with configurable project limits.
 * 
 * Features:
 * - Configurable project limit (default: 5)
 * - Loading states with skeleton UI
 * - Empty state with call-to-action
 * - "View All" navigation when more projects exist
 * - Responsive grid layout
 * 
 * @param limit - Maximum number of projects to display (default: 5)
 * @param showCreateButton - Whether to show "Create Project" button in empty state
 * @param variant - Display variant ('dashboard' | 'compact')
 * 
 * @example
 * // Default dashboard usage (shows 5 projects)
 * <DashboardProjectsSection />
 * 
 * // Custom limit with 3 projects
 * <DashboardProjectsSection limit={3} />
 * 
 * // Compact variant without create button
 * <DashboardProjectsSection 
 *   limit={10} 
 *   showCreateButton={false} 
 *   variant="compact" 
 * />
 */

interface DashboardProjectsSectionProps {
  limit?: number;
  showCreateButton?: boolean;
  variant?: 'dashboard' | 'compact';
}

export function DashboardProjectsSection({ 
  limit = DASHBOARD_CONFIG.DEFAULT_PROJECT_LIMIT,
  showCreateButton = true,
  variant = 'dashboard'
}: DashboardProjectsSectionProps) {
  const router = useRouter();
  
  // Use optimized hook for limited projects
  const { data: limitedProjects = [], isLoading, error } = useProjectsLimited(limit, 'updated');
  
  // Get total count for "more projects" indicator
  const { data: allProjects } = useProjects();
  const totalProjectsCount = Array.isArray(allProjects) ? allProjects.length : 0;
  const hasMoreProjects = totalProjectsCount > limit;

  return (
    <div className="space-y-6">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
          {/* Left side - Recent Projects (1 per row) */}
          <div className="lg:col-span-1">
            <Card className="bg-gray-50 dark:bg-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      Recent Projects
                    </CardTitle>
                    <CardDescription className="text-xs mt-1">
                      Your {limitedProjects.length} most recent projects
                      {hasMoreProjects && ` (${totalProjectsCount} total)`}
                    </CardDescription>
                  </div>
                  {hasMoreProjects && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push("/projects")}
                      className="text-xs"
                    >
                      View All
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {isLoading && (
                  <div className="space-y-3">
                    {/* Loading skeleton for projects */}
                    {Array.from({ length: Math.min(limit, 3) }).map((_, i) => (
                      <div key={i} className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                          </div>
                          <div className="h-4 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                {error && (
                  <div className="text-center py-6 text-destructive">
                    <p className="text-sm font-medium">Failed to load projects</p>
                    <p className="text-xs mt-1 text-muted-foreground">
                      Please try refreshing the page
                    </p>
                  </div>
                )}
                {limitedProjects.length === 0 && !isLoading && !error && (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                      No projects yet
                    </h3>
                    <p className="text-xs text-muted-foreground mb-4">
                      Create your first project to get started with your business planning
                    </p>
                    {showCreateButton && (
                      <Button
                        size="sm"
                        onClick={() => router.push("/user-dashboard")}
                        className="text-xs"
                      >
                        Create Your First Project
                      </Button>
                    )}
                  </div>
                )}
                <div className="grid gap-3 grid-cols-1 lg:grid-cols-2">
                  {/* Project Cards */}
                  {limitedProjects.map((project: any) => {
                    const lastUpdated = project.updatedAt || project.createdAt;
                    const isRecentlyUpdated =
                      project.updatedAt &&
                      new Date(project.updatedAt).getTime() !==
                        new Date(project.createdAt).getTime();

                    return (
                      <Card
                        key={project.id}
                        className="bg-gray-200/50 dark:bg-secondary/90 hover:shadow-md hover:bg-[var(--accent)]/10 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group py-1 hover:border-1 hover:border-[var(--accent)]"
                        onClick={() => router.push(`/projects/${project.id}`)}
                      >
                        <CardContent className="px-4 py-2">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium truncate">
                                  {project.name}
                                </h3>
                                <div className="flex items-center gap-2 ml-2">
                                  <ProjectProgress projectId={project.id} />
                                </div>
                              </div>

                              {lastUpdated && (
                                <div className="flex justify-between gap-2">
                                  <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
                                    <Clock className="h-3 w-3" />
                                    <span>
                                      {isRecentlyUpdated
                                        ? "Updated"
                                        : "Created"}{" "}
                                      {formatRelativeTime(lastUpdated)}
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <ProjectCounts projectId={project.id} />
                                  </div>
                                </div>
                              )}
                              {/* Project counts */}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
                {hasMoreProjects && (
                  <div className="text-center pt-4 border-t">
                    <p className="text-xs text-muted-foreground mb-2">
                      Showing {limitedProjects.length} of {totalProjectsCount} projects
                    </p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push("/projects")}
                      className="text-xs"
                    >
                      View All Projects →
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProjectCounts({ projectId }: { projectId: string }) {
  const { data: topics } = useAllProjectTopics(projectId);
  const { data: entriesByTopic } = useAllTopicEntries(
    projectId,
    Array.isArray(topics)
      ? topics.map((t) => ({
          sectionId: t.mappedCategoryId ?? t.sectionId,
          topicId: t.topicId,
          numericTopicId: t.numericTopicId,
          title: t.title,
          mappedLabel: t.mappedLabel,
        }))
      : [],
    "initial"
  );

  // Merge entries across all topics (prefer store not available here; merge backend only)
  const allEntries = (() => {
    const list: any[] = [];
    if (!entriesByTopic) return list;
    for (const key of Object.keys(entriesByTopic)) {
      const arr = entriesByTopic[key] || [];
      for (const e of arr) list.push(e);
    }
    return list;
  })();

  const {
    ideas,
    actions,
    confirmed: results,
  } = computeEntryCounts(allEntries as any[]);

  const handleDeleteProject = async (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.stopPropagation();
    e.preventDefault();
    if (!confirm("Delete this project? This action cannot be undone.")) return;
    try {
      await authApi.deleteProject(projectId);
      invalidateQueries.projects();
    } catch (err) {
      console.error("Failed to delete project", err);
    }
  };

  return (
    <div className="mt-2 space-y-1">
      <div className="flex items-center gap-2 text-[10px]">
        <StatusCountBadge type="idea" count={ideas} showTooltip />
        <StatusCountBadge type="action" count={actions} showTooltip />
        <StatusCountBadge type="confirmed" count={results} showTooltip />
        {process.env.NODE_ENV === "development" && (
          <IButton
            layout="icon-only"
            size="sm"
            variant="outline"
            hoverColor="grey"
            icon={Trash}
            aria-label="Delete project"
            iconClassName="text-[var(--destructive)]"
            onClick={handleDeleteProject}
          />
        )}
      </div>
    </div>
  );
}

function ProjectProgress({ projectId }: { projectId: string }) {
  const { data: topics } = useAllProjectTopics(projectId);
  const { data: entriesByTopic } = useAllTopicEntries(
    projectId,
    Array.isArray(topics)
      ? topics.map((t) => ({
          sectionId: t.mappedCategoryId ?? t.sectionId,
          topicId: t.topicId,
          numericTopicId: t.numericTopicId,
          title: t.title,
          mappedLabel: t.mappedLabel,
        }))
      : [],
    "initial"
  );

  if (!topics || !entriesByTopic) {
    return <Logo size={24} animated={true} />;
  }

  const totalTopics = Array.isArray(topics) ? topics.length : 0;
  const completedTopics = totalTopics
    ? topics.reduce((count, t) => {
        const entries = entriesByTopic?.[t.topicId] || [];
        const isConfirmed =
          Array.isArray(entries) &&
          entries.some((e: any) => e.status === "confirmed");
        return count + (isConfirmed ? 1 : 0);
      }, 0)
    : 0;
  const progress = totalTopics
    ? Math.round((completedTopics / totalTopics) * 100)
    : 0;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <p className="text-xs text-muted-foreground">{progress}%</p>
      </TooltipTrigger>
    </Tooltip>
  );
}
