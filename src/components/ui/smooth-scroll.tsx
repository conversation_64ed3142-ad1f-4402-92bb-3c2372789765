"use client";

import { React<PERSON>enis } from "lenis/react";
import React, { forwardRef } from "react";
import Image from "next/image";
import { HeroSection } from "../landing/hero-section";
import { DottedBackground } from "./dotted-background";
import { WaitlistSection } from "./waitlist-section";

const Component = forwardRef<HTMLElement>((props, ref) => {
  return (
    <ReactLenis root>
      <main ref={ref}>
        <article>
          <section className=" place-content-center sticky top-0">
            <HeroSection />
          </section>
          {process.env.NODE_ENV === "development" && (
            <section className="h-screen bg-[var(--siift-light-accent)] place-content-center sticky top-0 gap-3 flex flex-col items-center">
              <DottedBackground
                fadeEdge={20}
                dotSizes={[1, 1.5, 2]}
                spacing={25}
                dotsPerRow={8}
                opacity={0.01}
                darkOpacity={0.15}
                className="pointer-events-none bg-[var(--siift-light-accent)]/30"
              />
              <div className="text-center text-6xl font-bold">
                stuck on ideas?
              </div>
              <div className="text-center text-lg text-muted-foreground">
                go from fragmented chats to real traction, with a systematic
                execution engine
              </div>
              <div className="mt-6 flex justify-center px-4 max-w-xl mx-auto">
                <Image
                  src="https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/6878411b6a9788d867ddae05_output5.gif"
                  alt="siift output demo"
                  width={600}
                  height={405}
                  className="w-full max-w-xl h-auto rounded-lg border border-[var(--border)] shadow-sm"
                  priority
                />
              </div>
            </section>
          )}

          {process.env.NODE_ENV === "development" && (
            <section className="h-screen bg-[var(--siift-light-tooltip)] place-content-center sticky top-0">
              <DottedBackground
                fadeEdge={20}
                dotSizes={[1, 1.5, 2]}
                spacing={25}
                dotsPerRow={8}
                opacity={0.01}
                darkOpacity={0.15}
                className="pointer-events-none bg-[var(--siift-light-accent)]/30"
              />
              <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 items-center px-4">
                <div className="order-1 md:order-none flex justify-center">
                  <Image
                    src="https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/68680b2cd380a09d8fb764ab_sift%20table%20focus.avif"
                    alt="siift table focus"
                    width={600}
                    height={600}
                    className="w-full h-auto rounded-lg border border-[var(--border)] shadow-sm"
                  />
                </div>
                <div className="text-left md:text-left gap-6 flex flex-col">
                  <div className="text-6xl font-bold">
                    focus on what matters.
                  </div>
                  <div className="text-lg text-muted-foreground mt-2 text-balance font-bold">
                    See the big picture without losing sight of key details
                  </div>
                  <div className="text-md text-muted-foreground text-balance">
                    Understand how all the pieces of your business fit together
                    while staying focused on the critical tasks that drive
                    success. Get a holistic view of your startup's potential and
                    challenges.
                  </div>
                </div>
              </div>
            </section>
          )}
          {process.env.NODE_ENV === "development" && (
            <section className="h-screen bg-[var(--siift-lightest-accent)] place-content-center sticky top-0">
              <DottedBackground
                fadeEdge={20}
                dotSizes={[1, 1.5, 2]}
                spacing={25}
                dotsPerRow={8}
                opacity={0.01}
                darkOpacity={0.15}
                className="pointer-events-none bg-[var(--siift-light-accent)]/30"
              />
              <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8 items-center px-4">
                <div className="text-left md:text-left gap-6 flex flex-col">
                  <div className="text-6xl font-bold">
                    don't get lost in conversation.
                  </div>
                  <div className="text-lg text-muted-foreground mt-2 text-balance font-bold">
                    Manage blindspots, biases and distractions with confidence
                  </div>
                  <div className="text-md text-muted-foreground text-balance">
                    <li>Identify & overcome hidden obstacles</li>
                    <li>Keep key info on hand, not buried in a chat</li>
                    <li>Track assumptions & dependancies</li>
                  </div>
                </div>
                <div className="order-1 md:order-none flex justify-center">
                  <Image
                    src="https://cdn.prod.website-files.com/681148b70a35c1dc56aabe23/68680b5fe9a327e58eef82d1_sifted%20artifacts.avif"
                    alt="siift table focus"
                    width={600}
                    height={600}
                    className="w-full h-auto rounded-lg border border-[var(--border)] shadow-sm"
                  />
                </div>
              </div>
            </section>
          )}

          <section className=" min-h-screen bg-[var(--background)] place-content-center sticky top-0">
            <DottedBackground
              fadeEdge={20}
              dotSizes={[1, 1.5, 2]}
              spacing={25}
              dotsPerRow={8}
              opacity={0.01}
              darkOpacity={0.15}
              className="pointer-events-none bg-[var(--siift-light-accent)]/30"
            />
            <WaitlistSection />
          </section>
        </article>
      </main>
    </ReactLenis>
  );
});

Component.displayName = "Component";

export default Component;
