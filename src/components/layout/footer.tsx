"use client";

import { Logo } from "@/components/ui/logo";
import { Linkedin, X, Youtube } from "lucide-react";
import Link from "next/link";

export function Footer() {
  return (
    <footer className=" bg-[var(--siift-lightest-accent)]/20 mx-3 mb-3 rounded-lg border-[1px] border-border">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center space-y-6">
          {/* Company Info */}
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <Logo
                size={32}
                animated={false}
                showText={true}
                textSize={32}
                href="/"
              />
            </div>
            <p className="text-sm text-muted-foreground max-w-md">
              Streamline your project management with intelligent automation and
              insights.
            </p>
          </div>

          {/* Social Links */}
          <div className="flex space-x-4">
            <Link
              href="https://x.com/siiftAi"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <X className="h-5 w-5" />
            </Link>
            <Link
              href="https://www.linkedin.com/company/siiftai/posts/?feedView=all"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <Linkedin className="h-5 w-5" />
            </Link>
            <Link
              href="https://www.youtube.com/channel/UCcm9zM8lMavWGKBEDdupRiw"
              target="_blank"
              rel="noopener noreferrer"
              className="text-muted-foreground hover:text-foreground"
            >
              <Youtube className="h-5 w-5" />
            </Link>
          </div>

          {/* Page Links */}
          <div className="flex space-x-6">
            <Link
              href="/about"
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              About
            </Link>
            <Link
              href="/blog"
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              Blog
            </Link>
          </div>

          {/* Copyright */}
          <div className="text-sm text-muted-foreground">
            © {new Date().getFullYear()} siift. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
}
