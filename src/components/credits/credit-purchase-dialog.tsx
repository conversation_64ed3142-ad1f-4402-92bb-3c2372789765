"use client";

import { useState } from "react";
import { Coins, CreditCard, Loader2 } from "lucide-react";
import { useCreditsStore } from "@/stores/creditsStore";
import { useToast } from "@/hooks/useToast";
import { loadStripe } from "@stripe/stripe-js";
import { Elements } from "@stripe/react-stripe-js";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { StripePaymentForm } from "./stripe-payment-form";

interface CreditPurchaseDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const CREDIT_PACKAGES = [
  { credits: 50, price: 9.99, popular: false },
  { credits: 100, price: 19.99, popular: true },
  { credits: 200, price: 39.99, popular: false },
  { credits: 500, price: 99.99, popular: false },
];

const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

export function CreditPurchaseDialog({
  isOpen,
  onClose,
}: CreditPurchaseDialogProps) {
  const { balance, addCredits } = useCreditsStore();
  const [selectedPackage, setSelectedPackage] = useState(CREDIT_PACKAGES[1]); // Default to 100 credits
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const { success, error } = useToast();

  const handlePackageSelect = (pkg: (typeof CREDIT_PACKAGES)[0]) => {
    setSelectedPackage(pkg);
    setShowPaymentForm(false);
  };

  const handlePaymentSuccess = () => {
    addCredits(selectedPackage.credits);
    success(
      `Successfully added ${selectedPackage.credits} credits to your account.`
    );
    onClose();
    setShowPaymentForm(false);
  };

  const handlePaymentError = (errorMessage: string) => {
    error(errorMessage);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Coins className="h-5 w-5 text-[var(--primary)]" />
            Purchase Credits
          </DialogTitle>
          <DialogDescription>
            Choose a credit package to continue using Siift. You currently have{" "}
            <span className="font-semibold text-[var(--primary)]">
              {balance}
            </span>{" "}
            credits.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!showPaymentForm ? (
            <>
              {/* Credit Packages */}
              <div className="grid grid-cols-2 gap-3">
                {CREDIT_PACKAGES.map((pkg) => (
                  <div
                    key={pkg.credits}
                    className={`relative p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                      selectedPackage.credits === pkg.credits
                        ? "border-[var(--primary)] bg-[var(--primary)]/5"
                        : "border-border hover:border-[var(--primary)]/50"
                    }`}
                    onClick={() => handlePackageSelect(pkg)}
                  >
                    {pkg.popular && (
                      <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[var(--primary)] text-[var(--primary-foreground)]">
                        Popular
                      </Badge>
                    )}
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        {pkg.credits}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        credits
                      </div>
                      <div className="text-lg font-semibold text-[var(--primary)] mt-1">
                        ${pkg.price}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Purchase Button */}
              <Button
                onClick={() => setShowPaymentForm(true)}
                className="w-full bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]"
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Purchase {selectedPackage.credits} Credits - $
                {selectedPackage.price}
              </Button>

              {/* Additional Info */}
              <div className="text-xs text-muted-foreground text-center">
                Secure payment powered by Stripe. Credits are added instantly
                after payment.
              </div>
            </>
          ) : (
            <>
              {/* Selected Package Summary */}
              <div className="p-4 border border-[var(--primary)] rounded-lg bg-[var(--primary)]/5">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground">
                    {selectedPackage.credits} Credits
                  </div>
                  <div className="text-lg font-semibold text-[var(--primary)]">
                    ${selectedPackage.price}
                  </div>
                </div>
              </div>

              {/* Stripe Payment Form */}
              <Elements stripe={stripePromise}>
                <StripePaymentForm
                  amount={selectedPackage.price}
                  credits={selectedPackage.credits}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                />
              </Elements>

              {/* Back Button */}
              <Button
                variant="outline"
                onClick={() => setShowPaymentForm(false)}
                className="w-full"
              >
                Back to Package Selection
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
