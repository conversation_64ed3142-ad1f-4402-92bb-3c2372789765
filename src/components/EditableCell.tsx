"use client";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { useEffect, useRef, useState } from "react";

interface EditableCellProps {
  id: string;
  field: keyof BusinessItemDetail;
  value: string;
  multiline?: boolean;
  className?: string;
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  newRowData?: any;
  disabled?: boolean;
  /** When true, the input/textarea should fill the entire container height while editing */
  fillContainer?: boolean;
}

export function EditableCell({
  id,
  field,
  value,
  multiline = false,
  className = "",
  editingCell,
  setEditingCell,
  onSave,
  newRowData,
  disabled = false,
  fillContainer = false,
}: EditableCellProps) {
  const [localValue, setLocalValue] = useState(value);
  const isEditing = editingCell?.id === id && editingCell?.field === field;
  const isNewRow = id === "new-row";
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  // Ensure textarea height is correct on mount/toggle/value changes while editing
  useEffect(() => {
    // When filling the container, we don't auto-resize; we let CSS handle height
    if (!isEditing || !multiline || fillContainer) return;
    const el = textAreaRef.current;
    if (!el) return;
    el.style.height = "auto";
    el.style.height = el.scrollHeight + "px";
  }, [isEditing, multiline, localValue, fillContainer]);

  // When entering edit mode, place caret at the end of text (textarea and input)
  useEffect(() => {
    if (!isEditing) return;
    // Defer to next frame to ensure element is mounted and focused
    requestAnimationFrame(() => {
      if (multiline && textAreaRef.current) {
        const el = textAreaRef.current;
        const len = el.value.length;
        try {
          el.setSelectionRange(len, len);
        } catch {}
        el.scrollTop = el.scrollHeight;
      } else if (!multiline && inputRef.current) {
        const el = inputRef.current;
        const len = el.value.length;
        try {
          el.setSelectionRange(len, len);
        } catch {}
      }
    });
  }, [isEditing, multiline]);

  // For new row, use newRowData, otherwise use the passed value
  const currentValue = isNewRow
    ? newRowData?.[field as keyof typeof newRowData] || ""
    : value;

  // Update local value when prop value changes
  useEffect(() => {
    const valueToUse = isNewRow
      ? newRowData?.[field as keyof typeof newRowData] || ""
      : value;
    setLocalValue(valueToUse);
  }, [value, isNewRow, newRowData, field]);

  const handleEdit = () => {
    if (disabled) return;
    setEditingCell({ id, field });
    const valueToUse = isNewRow
      ? newRowData?.[field as keyof typeof newRowData] || ""
      : value;
    setLocalValue(valueToUse);
  };

  const handleSaveAndExit = () => {
    onSave(id, field, localValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSaveAndExit();
    } else if (e.key === "Escape") {
      const valueToUse = isNewRow
        ? newRowData?.[field as keyof typeof newRowData] || ""
        : value;
      setLocalValue(valueToUse);
      setEditingCell(null);
    }
  };

  if (isEditing) {
    if (multiline) {
      return (
        <Textarea
          ref={textAreaRef}
          rows={fillContainer ? undefined : 1}
          value={localValue}
          onChange={(e) => {
            setLocalValue(e.target.value);
            requestAnimationFrame(() => {
              const el = textAreaRef.current;
              if (el) {
                if (!fillContainer) {
                  el.style.height = "auto";
                  el.style.height = el.scrollHeight + "px";
                }
              }
            });
          }}
          onInput={(e) => {
            const el = e.currentTarget as HTMLTextAreaElement;
            if (!fillContainer) {
              el.style.height = "auto";
              el.style.height = el.scrollHeight + "px";
            }
          }}
          onBlur={handleSaveAndExit}
          onKeyDown={handleKeyDown}
          className={`w-full ${
            fillContainer ? "h-full min-h-full" : "h-auto"
          } overflow-hidden border-none px-0 py-0 rounded-none bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:border-transparent outline-none resize-none`}
          style={fillContainer ? undefined : { minHeight: 0 }}
          autoFocus
        />
      );
    } else {
      return (
        <Input
          ref={inputRef}
          value={localValue}
          onChange={(e) => setLocalValue(e.target.value)}
          onBlur={handleSaveAndExit}
          onKeyDown={handleKeyDown}
          className={`w-full ${
            fillContainer ? "h-full" : ""
          } border-none px-0 py-0 rounded-none bg-transparent focus-visible:ring-0 focus-visible:border-transparent outline-none`}
          autoFocus
        />
      );
    }
  }

  // For new row, show different placeholder based on field
  const getNewRowPlaceholder = (field: string, disabled: boolean) => {
    if (disabled) {
      return "locked";
    }
    if (field === "title") {
      return "enter another idea...";
    } else if (field === "actions") {
      return "Add action steps...";
    } else if (field === "result") {
      return "Add results...";
    }
    return "Click to add...";
  };

  const displayValue =
    currentValue ||
    (isNewRow
      ? getNewRowPlaceholder(field, disabled)
      : disabled
      ? "locked"
      : "Click to edit...");

  // Check if this is a new row field that should be disabled (actions/result when title is empty)
  const isNewRowFieldDisabled =
    isNewRow && field !== "title" && !newRowData?.title?.trim();

  // Overall disabled state (either explicitly disabled or new row field disabled)
  const isDisabled = disabled || isNewRowFieldDisabled;

  return (
    <div
      className={`relative transition-colors rounded p-2 -m-2 w-full ${
        fillContainer ? "h-full" : ""
      } ${className} ${
        isNewRow ? "text-gray-500 dark:text-gray-400 italic" : ""
      } ${
        isDisabled
          ? "opacity-50 cursor-not-allowed"
          : "cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800"
      }`}
      onClick={isDisabled ? undefined : handleEdit}
      title={
        disabled
          ? "This field is locked - complete dependencies first"
          : isNewRowFieldDisabled
          ? "Fill in the idea first"
          : "Click to edit"
      }
    >
      {/* Angled lines background for disabled fields */}
      {isDisabled && (
        <div
          className="absolute inset-0 opacity-20 pointer-events-none"
          style={{
            backgroundImage: `repeating-linear-gradient(
              45deg,
              transparent,
              transparent 4px,
              hsl(from var(--muted-foreground) h s l / 0.3) 4px,
              hsl(from var(--muted-foreground) h s l / 0.3) 6px
            )`,
          }}
        />
      )}
      <div className="whitespace-pre-wrap break-words relative z-10">
        {displayValue}
      </div>
    </div>
  );
}
