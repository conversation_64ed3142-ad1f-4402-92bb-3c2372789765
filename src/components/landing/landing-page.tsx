"use client";

import { Footer } from "@/components/layout/footer";
import { DottedBackground } from "@/components/ui/dotted-background";
import { WaitlistSection } from "@/components/ui/waitlist-section";
import { HeroSection } from "./hero-section";
import SmoothScroll from "@/components/ui/smooth-scroll";

export function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <main className="flex-1">
        {/* Smooth scroll showcase between project input and waitlist */}
        <section className="relative">
          <SmoothScroll />
        </section>
      </main>
      <Footer />
    </div>
  );
}
