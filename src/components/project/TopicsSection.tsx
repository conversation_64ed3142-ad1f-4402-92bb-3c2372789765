"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { StatusCountBadge } from "@/components/ui/status-count-badge";
import { useProjectTopics } from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useBusinessItemStore } from "@/stores/businessItemStore";
import { computeEntryCounts } from "@/utils/entryCounts";
import type { BusinessItem, BusinessItemDetail } from "@/types/BusinessSection.types";
import { useMemo } from "react";
import { Lightbulb } from "lucide-react";

type TopicsSectionProps = {
  projectId: string;
};

export function TopicsSection({ projectId }: TopicsSectionProps) {
  const { data = {}, isLoading, error } = useProjectTopics(projectId);
  const { getTopicEntries } = useBusinessSectionStore();
  const { setSelectedItem, setItemDetails } = useBusinessItemStore();

  // Enrich topics with entry counts from stores
  const enrichedGroups = useMemo(() => {
    if (!data || isLoading || error) return [];

    const groupEntries = Object.entries(data ?? {});
    
    return groupEntries.map(([groupName, topics]) => {
      const enrichedTopics = Array.isArray(topics) 
        ? topics
            .filter((t: any) => !t.title.toLowerCase().includes('market size')) // Remove hardcoded filter later
            .map((topic: any) => {
              // Get entries from store
              const entries = getTopicEntries(
                topic.mappedCategoryId ?? topic.sectionId, 
                String(topic.numericTopicId)
              ) as any[];
              
              // Calculate counts
              const counts = computeEntryCounts(entries || []);
              const totalCount = counts.ideas + counts.actions + counts.confirmed;
              
              return {
                ...topic,
                entries,
                counts,
                totalCount,
                hasProgress: counts.actions > 0 || counts.confirmed > 0,
              };
            })
            .filter((t: any) => t.totalCount > 0) // Only show topics with entries
            .sort((a: any, b: any) => {
              // Sort by progress first, then by total count
              if (a.hasProgress && !b.hasProgress) return -1;
              if (!a.hasProgress && b.hasProgress) return 1;
              return b.totalCount - a.totalCount;
            })
        : [];

      return {
        groupName,
        topics: enrichedTopics,
      };
    }).filter(group => group.topics.length > 0); // Only show groups with topics
  }, [data, isLoading, error, getTopicEntries]);

  // Handle topic click
  const handleTopicClick = (topic: any) => {
    // Convert entries to BusinessItemDetail format
    const itemDetails: BusinessItemDetail[] = topic.entries.map((e: any) => ({
      id: String(e.id),
      title: e.title || e.action || e.idea || e.result || '',
      actions: e.actions || e.action || '',
      result: e.result || '',
      status: e.status || "idea",
    }));

    // Create BusinessItem
    const businessItem: BusinessItem & {
      sectionId?: string;
      topicId?: string;
      numericTopicId?: number;
    } = {
      id: topic.id,
      title: topic.title,
      status: topic.hasProgress ? "action" : "idea",
      actions: topic.counts.actions,
      ideas: topic.counts.ideas,
      results: topic.counts.confirmed,
      icon: Lightbulb, // Default topic icon
      sectionId: topic.mappedCategoryId ?? topic.sectionId,
      topicId: topic.topicId || topic.id,
      numericTopicId: topic.numericTopicId,
    };

    setSelectedItem(businessItem);
    setItemDetails(itemDetails);
  };

  if (isLoading) {
    return (
      <div className="mt-6 text-sm text-muted-foreground">Loading topics…</div>
    );
  }

  if (error) {
    return (
      <div className="mt-6 text-sm text-red-600">Failed to load topics</div>
    );
  }

  if (!enrichedGroups.length) {
    return (
      <div className="mt-6">
        <h2 className="text-base font-semibold mb-2">Topics</h2>
        <div className="text-sm text-muted-foreground">
          No topics with entries yet. Start adding ideas to see them here.
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6">
      <h2 className="text-base font-semibold mb-2">Topics</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {enrichedGroups.map(({ groupName, topics }) => (
          <Card key={groupName} className="bg-white dark:bg-card border border-border/60">
            <CardHeader className="py-2 px-4">
              <CardTitle className="text-sm font-medium truncate">{groupName}</CardTitle>
            </CardHeader>
            <CardContent className="py-2 px-4">
              <ul className="space-y-2">
                {topics.map((topic: any) => (
                  <li 
                    key={topic.id} 
                    className="flex items-center justify-between p-2 rounded hover:bg-muted/50 cursor-pointer transition-colors"
                    onClick={() => handleTopicClick(topic)}
                  >
                    <span className="text-sm text-foreground truncate flex-1">
                      {topic.title}
                    </span>
                    <div className="flex items-center gap-1 ml-2 flex-shrink-0">
                      <StatusCountBadge type="idea" count={topic.counts.ideas} />
                      <StatusCountBadge type="action" count={topic.counts.actions} />
                      <StatusCountBadge type="confirmed" count={topic.counts.confirmed} />
                    </div>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}


