"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ProgressBarProps {
  progress: number;
  totalTasks?: number;
  completedTasks?: number;
  inProgressTasks?: number;
  pendingTasks?: number;
}

export function ProgressBar({ progress }: ProgressBarProps) {
  return (
    <TooltipProvider>
      <div className="w-full relative flex items-center gap-3">
        {/* Progress bar with tooltip only (no click) */}
        <div className="flex-2 w-full">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="w-full h-2.5 rounded border border-[var(--progress-border)] bg-[var(--progress-bg)] relative overflow-hidden hover:border-[var(--primary)] hover:shadow-sm transition-all duration-200">
                {/* Progress fill with striped pattern */}
                <div
                  className="h-full rounded transition-all duration-300 relative"
                  style={{
                    width: `${progress}%`,
                    backgroundColor: "var(--progress-fill)",
                  }}
                >
                  {/* Horizontal striped pattern on progress fill */}
                  <div
                    className="absolute inset-0 opacity-20"
                    style={{
                      backgroundImage: `repeating-linear-gradient(
                          45deg,
                          transparent,
                          transparent 1px,
                          rgba(255, 255, 255, 0.3) 1px,
                          rgba(255, 255, 255, 0.3) 2px
                        )`,
                    }}
                  />
                </div>
                <div
                  className="absolute inset-0 opacity-30"
                  style={{
                    backgroundImage: `repeating-linear-gradient(
                        45deg,
                        transparent,
                        transparent 1.5px,
                        rgba(156, 163, 175, 0.4) 1.5px,
                        rgba(156, 163, 175, 0.4) 3px
                      )`,
                  }}
                />
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p className="font-medium ">Project progress</p>
              <p className="text-xs ">{progress}% complete</p>
            </TooltipContent>
          </Tooltip>
        </div>
        {/* Progress percentage on the right */}
        <span className="text-xs font-medium text-[var(--progress-text)] whitespace-nowrap">
          {progress}%
        </span>
      </div>
    </TooltipProvider>
  );
}
