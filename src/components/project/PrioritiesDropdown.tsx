"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IButton } from "@/components/ui/ibutton";
import { memo, useMemo, useState } from "react";
import { Badge } from "@/components/ui/badge";
import {
  Lightbulb,
  Zap,
  Check,
  AlertTriangle,
  Users,
  BarChart3,
  TrendingUp,
  Star,
  Package,
  Settings,
  Target,
  Megaphone,
  Palette,
  MessageCircle,
  FileText,
  Building,
  DollarSign,
  Shield,
  Heart,
  Maximize2,
} from "lucide-react";
import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { Logo } from "../ui/logo";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useBusinessItemStore } from "@/stores/businessItemStore";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { computeEntryCounts } from "@/utils/entryCounts";
import { StatusCountBadge } from "../ui/status-count-badge";

// Safely normalize any value to a short display string
const toText = (value: unknown): string => {
  if (value == null) return "";
  if (typeof value === "string") return value;
  if (typeof value === "number" || typeof value === "boolean")
    return String(value);
  if (Array.isArray(value))
    return value
      .map((v) => toText(v))
      .filter(Boolean)
      .join(" ");
  if (typeof value === "object") {
    const v: any = value;
    const candidate = [
      v.title,
      v.idea,
      v.action,
      v.result,
      v.text,
      v.content,
    ].find((x) => typeof x === "string" && x.trim().length > 0);
    return candidate ? String(candidate) : ""; // avoid JSON/stringifying objects into UI
  }
  return "";
};

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return AlertTriangle;
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return Users;
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return Lightbulb;
  if (titleLower.includes("market") || titleLower.includes("size"))
    return BarChart3;
  if (titleLower.includes("trend")) return TrendingUp;
  if (titleLower.includes("value") || titleLower.includes("uvp")) return Star;
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return Package;
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return Settings;
  if (titleLower.includes("positioning")) return Target;
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return Megaphone;
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return Palette;
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return MessageCircle;
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return FileText;
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return Building;
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return DollarSign;
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return Shield;
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return Heart;
  return Lightbulb; // Default fallback
};

export const PrioritiesDropdown = memo(function PrioritiesDropdown({
  allTopics,
  entriesByTopic,
  isLoading = false,
}: {
  allTopics?: ProjectTopicSummary[];
  entriesByTopic?: Record<string, TopicEntry[]>;
  isLoading?: boolean;
}) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { getTopicEntries, sections } = useBusinessSectionStore();
  const { setSelectedItem, setItemDetails } = useBusinessItemStore();

  // Build topic-level priorities with badges from store data (much simpler!)
  const topicPriorities = useMemo(() => {
    if (!allTopics || allTopics.length === 0) return [];

    const topicMap = new Map<string, {
      id: string;
      title: string;
      topicId: string;
      sectionId: string;
      numericTopicId: number;
      topicIcon: any;
      counts: { ideas: number; actions: number; confirmed: number };
      totalCount: number;
      hasProgress: boolean;
    }>();

    // Process each topic and get counts from store
    allTopics.forEach((topic) => {
      const normalizedTitle = (topic.mappedLabel || topic.title).toLowerCase().trim();
      const sectionKey = topic.mappedCategoryId ?? topic.sectionId;
      
      // Get entries from store (single source of truth)
      const entries = getTopicEntries(sectionKey, String(topic.numericTopicId)) as any[];
      if (!entries || entries.length === 0) return;

      // Calculate counts using shared helper
      const counts = computeEntryCounts(entries);
      const totalCount = counts.ideas + counts.actions + counts.confirmed;
      const hasProgress = counts.actions > 0 || counts.confirmed > 0;

      if (totalCount === 0) return; // Skip topics with no entries

      // Use existing or create new topic entry (handle duplicates)
      const existing = topicMap.get(normalizedTitle);
      if (existing) {
        // Merge counts for duplicate topics
        existing.counts.ideas += counts.ideas;
        existing.counts.actions += counts.actions;
        existing.counts.confirmed += counts.confirmed;
        existing.totalCount += totalCount;
        existing.hasProgress = existing.hasProgress || hasProgress;
      } else {
        const topicIcon = getTopicIcon(topic.mappedLabel || topic.title);
        topicMap.set(normalizedTitle, {
          id: topic.topicId,
          title: topic.mappedLabel || topic.title,
          topicId: topic.topicId,
          sectionId: sectionKey,
          numericTopicId: topic.numericTopicId,
          topicIcon,
          counts,
          totalCount,
          hasProgress,
        });
      }
    });

    // Convert to array and sort by priority: progress first, then by total activity
    const topics = Array.from(topicMap.values());
    topics.sort((a, b) => {
      // Topics with progress (actions/confirmed) first
      if (a.hasProgress && !b.hasProgress) return -1;
      if (!a.hasProgress && b.hasProgress) return 1;
      
      // Then by total activity count
      return b.totalCount - a.totalCount;
    });

    return topics;
  }, [allTopics, getTopicEntries]);

  // Limit to top N topics for display
  const displayedTopics = useMemo(
    () => topicPriorities.slice(0, 5),
    [topicPriorities]
  );
  const totalCount = displayedTopics.length;
  const allCount = topicPriorities.length;

  // Handle topic click - navigate to selected topic
  const handleTopicClick = (topic: any) => {
    // Get entries from store for this topic
    const entries = getTopicEntries(topic.sectionId, String(topic.numericTopicId)) as any[];
    
    // Convert to BusinessItemDetail format
    const itemDetails: BusinessItemDetail[] = entries.map((e: any) => ({
      id: String(e.id),
      title: toText(e.title) || toText(e.action) || toText(e.idea) || toText(e.result),
      actions: toText(e.actions) || toText(e.action),
      result: toText(e.result),
      status: e.status || "idea",
    }));

    // Create a BusinessItem from this topic
    const businessItem: BusinessItem & {
      sectionId?: string;
      topicId?: string;
      numericTopicId?: number;
      isUnlocked?: boolean;
      lockedDependencies?: string[];
    } = {
      id: topic.id,
      title: topic.title,
      status: topic.hasProgress ? "action" : "idea",
      actions: topic.counts.actions,
      ideas: topic.counts.ideas,
      results: topic.counts.confirmed,
      icon: topic.topicIcon,
      sectionId: topic.sectionId,
      topicId: topic.topicId,
      numericTopicId: topic.numericTopicId,
    };

    setSelectedItem(businessItem);
    setItemDetails(itemDetails);
  };

  // Handle entry click - navigate to parent topic (legacy, keeping for compatibility)
  const handleEntryClick = (item: any) => {
    // Build full entries list for the parent topic (merge backend + store)
    const toDetail = (e: any): BusinessItemDetail => ({
      id: String(e.id),
      title: (
        toText(e.title) ||
        toText((e as any).content) ||
        toText(e.action) ||
        toText(e.idea) ||
        toText(e.result)
      ).trim(),
      actions: toText((e as any).actions) || toText((e as any).action),
      result: toText((e as any).result),
      status: (e.status as any) || "idea",
    });

    const backendEntriesRaw = (entriesByTopic?.[item.topicId] ?? []) as any[];
    const backendEntries = backendEntriesRaw.map(toDetail);
    const storeEntriesRaw = getTopicEntries(
      item.sectionId,
      String(item.numericTopicId)
    ) as any[];
    const storeEntries = (storeEntriesRaw || []).map(toDetail);

    const mergedMap = new Map<string, BusinessItemDetail>();
    backendEntries.forEach((e) => mergedMap.set(e.id, e));
    storeEntries.forEach((e) => mergedMap.set(e.id, e));
    const mergedEntries = Array.from(mergedMap.values());

    const businessItem: BusinessItem & {
      entries?: BusinessItemDetail[];
      sectionId?: string;
      isUnlocked?: boolean;
      lockedDependencies?: string[];
    } = {
      id: String(item.numericTopicId),
      title: item.topic,
      status:
        (mergedEntries[0]?.status as any) || (item.status as any) || "idea",
      ...(() => {
        const { actions, ideas, confirmed } = computeEntryCounts(
          mergedEntries as any[]
        );
        return { actions, ideas, results: confirmed };
      })(),
      icon: item.topicIcon,
      sectionId: item.sectionId,
      entries: mergedEntries,
      isUnlocked: true,
      lockedDependencies: [],
    };
    // Debug: inspect object sent from PrioritiesDropdown
    console.log("[PrioritiesDropdown] setSelectedItem()", {
      id: businessItem.id,
      title: businessItem.title,
      status: businessItem.status,
      counts: {
        ideas: businessItem.ideas,
        actions: businessItem.actions,
        results: (businessItem as any).results,
      },
      entriesCount: businessItem.entries?.length || 0,
    });
    setSelectedItem(businessItem);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <IButton
            text={isLoading ? "Loading..." : "Priorities"}
            badge={isLoading ? "" : (allCount || "")}
            variant="ghost"
            className="bg-[var(--accent)]/10"
            hoverScale={true}
            showBorder={true}
            hoverColor="grey"
            textClassName=" text-lg"
            disabled={isLoading}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
          align="end"
          side="bottom"
          sideOffset={4}
        >
          <DropdownMenuLabel className="text-muted-foreground text-sm">
            {totalCount === 0
              ? "No priorities yet"
              : `Top priorities (${totalCount})`}
          </DropdownMenuLabel>
          <div className="mt-1 divide-y divide-[var(--border)]">
            {displayedTopics.length === 0 ? (
              <div className="p-4 text-center">
                <div className="text-muted-foreground text-sm">
                  {!allTopics || allTopics.length === 0
                    ? "No topics available"
                    : "No priorities yet"}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {!allTopics || allTopics.length === 0
                    ? "Topics will appear as they are created for this project."
                    : "Add entries to topics to see them here."}
                </div>
              </div>
            ) : (
              displayedTopics.map((topic) => {
                const TopicIcon = topic.topicIcon;

                return (
                  <DropdownMenuItem
                    key={topic.id}
                    className="flex items-center gap-3 p-3 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                    onClick={() => handleTopicClick(topic)}
                  >
                    <TopicIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <div className="flex flex-col flex-1 min-w-0">
                      <span className="text-sm text-foreground font-medium truncate">
                        {topic.title}
                      </span>
                      <div className="flex items-center gap-1 mt-1">
                        <StatusCountBadge type="idea" count={topic.counts.ideas} />
                        <StatusCountBadge type="action" count={topic.counts.actions} />
                        <StatusCountBadge type="confirmed" count={topic.counts.confirmed} />
                      </div>
                    </div>
                  </DropdownMenuItem>
                );
              })
            )}
          </div>
          {allCount > 5 ? (
            <div className="p-1">
              <DropdownMenuItem
                className="flex items-center gap-2 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                onClick={() => setIsSheetOpen(true)}
              >
                <Maximize2 className="h-4 w-4 text-[var(--muted-foreground)]" />
                <span className="text-sm">Expand to view all ({allCount})</span>
              </DropdownMenuItem>
            </div>
          ) : null}
        </DropdownMenuContent>
      </DropdownMenu>
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent side="right" className="w-[50vw] max-w-[50vw] min-h-0">
          <SheetHeader>
            <SheetTitle>All priorities ({allCount})</SheetTitle>
          </SheetHeader>
          <ScrollArea className="flex-1 min-h-0 px-2 h-[calc(100vh-100px)]">
            <div className="divide-y divide-[var(--border)]">
              {topicPriorities.map((topic) => {
                const hasProgress = topic.hasProgress;
                const Icon = topic.topicIcon;

                return (
                  <div
                    key={topic.id}
                    className="flex items-start gap-2 p-3 transition-colors hover:bg-[var(--siift-bold-accent)]/10 dark:hover:bg-[var(--siift-bold-accent)]/20 cursor-pointer"
                    onClick={() => {
                      handleTopicClick(topic);
                      setIsSheetOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2 mr-2">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="flex flex-col flex-1">
                      <span className="text-sm text-foreground font-medium">
                        {topic.title}
                      </span>
                      <div className="flex items-center gap-1 mt-1">
                        <StatusCountBadge type="idea" count={topic.counts.ideas} />
                        <StatusCountBadge type="action" count={topic.counts.actions} />
                        <StatusCountBadge type="confirmed" count={topic.counts.confirmed} />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </>
  );
});
