"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { IButton } from "@/components/ui/ibutton";
import { memo, useMemo, useState } from "react";
import {
  Lightbulb,
  AlertTriangle,
  Users,
  BarChart3,
  TrendingUp,
  Star,
  Package,
  Settings,
  Target,
  Megaphone,
  Palette,
  MessageCircle,
  FileText,
  Building,
  DollarSign,
  Shield,
  Heart,
  Maximize2,
} from "lucide-react";
import type { ProjectTopicSummary } from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useBusinessItemStore } from "@/stores/businessItemStore";
import type {
  BusinessItem,
  BusinessItemDetail,
} from "@/types/BusinessSection.types";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { computeEntryCounts } from "@/utils/entryCounts";

// Safely normalize any value to a short display string
const toText = (value: unknown): string => {
  if (value == null) return "";
  if (typeof value === "string") return value;
  if (typeof value === "number" || typeof value === "boolean")
    return String(value);
  if (Array.isArray(value))
    return value
      .map((v) => toText(v))
      .filter(Boolean)
      .join(" ");
  if (typeof value === "object") {
    const v: any = value;
    const candidate = [
      v.title,
      v.idea,
      v.action,
      v.result,
      v.text,
      v.content,
    ].find((x) => typeof x === "string" && x.trim().length > 0);
    return candidate ? String(candidate) : ""; // avoid JSON/stringifying objects into UI
  }
  return "";
};

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return AlertTriangle;
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return Users;
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return Lightbulb;
  if (titleLower.includes("market") || titleLower.includes("size"))
    return BarChart3;
  if (titleLower.includes("trend")) return TrendingUp;
  if (titleLower.includes("value") || titleLower.includes("uvp")) return Star;
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return Package;
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return Settings;
  if (titleLower.includes("positioning")) return Target;
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return Megaphone;
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return Palette;
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return MessageCircle;
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return FileText;
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return Building;
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return DollarSign;
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return Shield;
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return Heart;
  return Lightbulb; // Default fallback
};

export const PrioritiesDropdown = memo(function PrioritiesDropdown({
  allTopics,
  isLoading = false,
}: {
  allTopics?: ProjectTopicSummary[];
  isLoading?: boolean;
}) {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const { getTopicEntries } = useBusinessSectionStore();
  const { setSelectedItem, setItemDetails } = useBusinessItemStore();

  // Build entry-level priorities from store data
  const entryPriorities = useMemo(() => {
    if (!allTopics || allTopics.length === 0) return [];

    const allEntries: Array<{
      id: string;
      title: string;
      status: string;
      topicTitle: string;
      topicIcon: any;
      sectionId: string;
      numericTopicId: number;
      topicId: string;
      updatedAt?: string;
      createdAt?: string;
    }> = [];

    // Process each topic and collect all entries
    allTopics.forEach((topic) => {
      const sectionKey = topic.mappedCategoryId ?? topic.sectionId;
      const topicTitle = topic.mappedLabel || topic.title;
      const topicIcon = getTopicIcon(topicTitle);

      // Get entries from store (single source of truth)
      const entries = getTopicEntries(
        sectionKey,
        String(topic.numericTopicId)
      ) as any[];

      if (!entries || entries.length === 0) return;

      // Add each entry to the list with topic context
      entries.forEach((entry: any) => {
        const entryTitle =
          toText(entry.title) ||
          toText(entry.action) ||
          toText(entry.idea) ||
          toText(entry.result);
        if (!entryTitle.trim()) return; // Skip entries without meaningful content

        allEntries.push({
          id: String(entry.id),
          title: entryTitle,
          status: entry.status || "idea",
          topicTitle,
          topicIcon,
          sectionId: sectionKey,
          numericTopicId: topic.numericTopicId,
          topicId: topic.topicId,
          updatedAt: entry.updatedAt,
          createdAt: entry.createdAt,
        });
      });
    });

    // Sort entries by priority: confirmed > action > idea, then by recency
    allEntries.sort((a, b) => {
      // Status priority: confirmed > action > idea
      const statusPriority = { confirmed: 3, action: 2, idea: 1, unproven: 2 };
      const aPriority =
        statusPriority[a.status as keyof typeof statusPriority] || 0;
      const bPriority =
        statusPriority[b.status as keyof typeof statusPriority] || 0;

      if (aPriority !== bPriority) {
        return bPriority - aPriority;
      }

      // Then by recency (most recent first)
      const aTime = Date.parse(a.updatedAt || a.createdAt || "0");
      const bTime = Date.parse(b.updatedAt || b.createdAt || "0");
      if (Number.isFinite(bTime - aTime) && bTime !== aTime) {
        return bTime - aTime;
      }

      // Finally by ID (higher ID = more recent)
      return Number(b.id) - Number(a.id);
    });

    return allEntries;
  }, [allTopics, getTopicEntries]);

  // Limit to top N entries for display
  const displayedEntries = useMemo(
    () => entryPriorities.slice(0, 5),
    [entryPriorities]
  );
  const totalCount = displayedEntries.length;
  const allCount = entryPriorities.length;

  // Handle entry click - navigate to the topic containing this entry
  const handleEntryClick = (entry: any) => {
    // Get all entries for this topic from store
    const allTopicEntries = getTopicEntries(
      entry.sectionId,
      String(entry.numericTopicId)
    ) as any[];

    // Convert to BusinessItemDetail format
    const itemDetails: BusinessItemDetail[] = allTopicEntries.map((e: any) => ({
      id: String(e.id),
      title:
        toText(e.title) ||
        toText(e.action) ||
        toText(e.idea) ||
        toText(e.result),
      actions: toText(e.actions) || toText(e.action),
      result: toText(e.result),
      status: e.status || "idea",
    }));

    // Calculate counts for the topic
    const counts = computeEntryCounts(allTopicEntries);

    // Create a BusinessItem from this topic
    const businessItem: BusinessItem & {
      sectionId?: string;
      topicId?: string;
      numericTopicId?: number;
      isUnlocked?: boolean;
      lockedDependencies?: string[];
    } = {
      id: String(entry.numericTopicId),
      title: entry.topicTitle,
      status:
        counts.confirmed > 0
          ? "confirmed"
          : counts.actions > 0
          ? "action"
          : "idea",
      actions: counts.actions,
      ideas: counts.ideas,
      results: counts.confirmed,
      icon: entry.topicIcon,
      sectionId: entry.sectionId,
      topicId: entry.topicId,
      numericTopicId: entry.numericTopicId,
    };

    setSelectedItem(businessItem);
    setItemDetails(itemDetails);
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <IButton
            text={isLoading ? "Loading..." : "Priorities"}
            badge={isLoading ? "" : allCount || ""}
            variant="ghost"
            className="bg-[var(--accent)]/10"
            hoverScale={true}
            showBorder={true}
            hoverColor="grey"
            textClassName=" text-lg"
            disabled={isLoading}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-80 rounded-lg animate-in fade-in-0 zoom-in-95"
          align="end"
          side="bottom"
          sideOffset={4}
        >
          <DropdownMenuLabel className="text-muted-foreground text-sm">
            {totalCount === 0
              ? "No priorities yet"
              : `Top priorities (${totalCount})`}
          </DropdownMenuLabel>
          <div className="mt-1 divide-y divide-[var(--border)]">
            {displayedEntries.length === 0 ? (
              <div className="p-4 text-center">
                <div className="text-muted-foreground text-sm">
                  {!allTopics || allTopics.length === 0
                    ? "No topics available"
                    : "No priorities yet"}
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {!allTopics || allTopics.length === 0
                    ? "Topics will appear as they are created for this project."
                    : "Add entries to topics to see them here."}
                </div>
              </div>
            ) : (
              displayedEntries.map((entry) => {
                const EntryIcon = entry.topicIcon;
                const statusColor =
                  entry.status === "confirmed"
                    ? "text-green-600"
                    : entry.status === "action"
                    ? "text-yellow-600"
                    : "text-gray-500";

                return (
                  <DropdownMenuItem
                    key={entry.id}
                    className="flex items-center gap-3 p-3 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                    onClick={() => handleEntryClick(entry)}
                  >
                    <EntryIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <div className="flex flex-col flex-1 min-w-0">
                      <span className="text-sm text-foreground font-medium truncate">
                        {entry.title}
                      </span>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground truncate">
                          {entry.topicTitle}
                        </span>
                        <span className={`text-xs font-medium ${statusColor}`}>
                          {entry.status}
                        </span>
                      </div>
                    </div>
                  </DropdownMenuItem>
                );
              })
            )}
          </div>
          {allCount > 5 ? (
            <div className="p-1">
              <DropdownMenuItem
                className="flex items-center gap-2 rounded-md hover:bg-[var(--hover-muted)]/30 cursor-pointer"
                onClick={() => setIsSheetOpen(true)}
              >
                <Maximize2 className="h-4 w-4 text-[var(--muted-foreground)]" />
                <span className="text-sm">Expand to view all ({allCount})</span>
              </DropdownMenuItem>
            </div>
          ) : null}
        </DropdownMenuContent>
      </DropdownMenu>
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent side="right" className="w-[50vw] max-w-[50vw] min-h-0">
          <SheetHeader>
            <SheetTitle>All priorities ({allCount})</SheetTitle>
          </SheetHeader>
          <ScrollArea className="flex-1 min-h-0 px-2 h-[calc(100vh-100px)]">
            <div className="divide-y divide-[var(--border)]">
              {entryPriorities.map((entry) => {
                const Icon = entry.topicIcon;
                const statusColor =
                  entry.status === "confirmed"
                    ? "text-green-600"
                    : entry.status === "action"
                    ? "text-yellow-600"
                    : "text-gray-500";

                return (
                  <div
                    key={entry.id}
                    className="flex items-start gap-2 p-3 transition-colors hover:bg-[var(--siift-bold-accent)]/10 dark:hover:bg-[var(--siift-bold-accent)]/20 cursor-pointer"
                    onClick={() => {
                      handleEntryClick(entry);
                      setIsSheetOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-2 mr-2">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="flex flex-col flex-1">
                      <span className="text-sm text-foreground font-medium">
                        {entry.title}
                      </span>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {entry.topicTitle}
                        </span>
                        <span className={`text-xs font-medium ${statusColor}`}>
                          {entry.status}
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </>
  );
});
