"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Logo } from "@/components/ui/logo";
import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { AnimatePresence, motion } from "framer-motion";
import { BusinessItemTable } from "../business-item-table";
import { BusinessSectionsGrid } from "@/components/business-sections/BusinessSectionsGrid";
import { ProgressBar } from "./ProgressBar";
import { ContentSections } from "./ContentSections";
import { Card } from "@/components/ui/card";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useEffect, useRef } from "react";
import { Focusable } from "../common/Focusable";

interface ProjectMainContentProps {
  activeContent: "drafts" | "files" | null;
  setActiveContent: (content: "drafts" | "files" | null) => void;
  mockDraftItems: any[]; // deprecated
  mockFileItems: any[]; // deprecated
  selectedItem: any;
  itemDetails: any[];
  sections: any[];
  isLoading: boolean;
  error: string | null;
  onBusinessItemClick: (item: any) => void;
  onBackToItems: () => void;
  projectId?: string;
  allTopics?: ProjectTopicSummary[];
  entriesByTopic?: Record<string, TopicEntry[]>;
  isDependencyLoading?: boolean;
  progress?: number;
}

export function ProjectMainContent({
  activeContent,
  setActiveContent,
  selectedItem,
  itemDetails,
  sections,
  isLoading,
  error,
  onBusinessItemClick,
  onBackToItems,
  projectId,
  allTopics,
  entriesByTopic,
  isDependencyLoading,
  progress = 0,
}: ProjectMainContentProps) {
  const { setTopicEntries, applyEntryLimitsToAllTopics, getTopicEntries } =
    useBusinessSectionStore();
  const syncTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Status computation utilities (matching BusinessItemTable logic)
  const parseActionsSafe = (
    actions: string
  ): Array<{ progress?: number; results?: Array<{ content?: string }> }> => {
    if (!actions || typeof actions !== "string") return [];
    try {
      const parsed = JSON.parse(actions);
      const items = Array.isArray(parsed)
        ? parsed
        : Array.isArray((parsed as any)?.actions)
        ? (parsed as any).actions
        : [];
      return Array.isArray(items) ? items : [];
    } catch {
      return [];
    }
  };

  const isActionsProgressComplete = (actions: string): boolean => {
    const items = parseActionsSafe(actions);
    return items.some((a) => Number(a?.progress) === 100);
  };

  const hasAnyResultInActions = (actions: string): boolean => {
    const items = parseActionsSafe(actions);
    return items.some((a) => (a.results?.[0]?.content || "").trim().length > 0);
  };

  const recomputeEntryStatus = (
    title: string,
    actions: string,
    result: string
  ): "idea" | "action" | "unproven" | "confirmed" => {
    const hasTitle = Boolean(title?.trim());
    const hasAction =
      Boolean(actions?.trim()) || parseActionsSafe(actions).length > 0;
    const hasResult = Boolean(result?.trim()) || hasAnyResultInActions(actions);

    if (hasResult) {
      return isActionsProgressComplete(actions) ? "confirmed" : "unproven";
    }
    if (hasAction) return "action";
    if (hasTitle) return "idea";
    return "idea";
  };

  // Coalesce backend-fetched entries into the local store so the grid
  // never renders empty while queries are refetching.
  useEffect(() => {
    if (!allTopics || !entriesByTopic) return;

    // Build topicId -> sectionId map from current topics
    const idToSection = new Map<string, string>();
    const topicIdToNumericId = new Map<string, string>();
    allTopics.forEach((t) => {
      idToSection.set(String(t.topicId), String(t.sectionId));
      topicIdToNumericId.set(String(t.topicId), String(t.numericTopicId));
    });

    if (syncTimerRef.current) clearTimeout(syncTimerRef.current);
    syncTimerRef.current = setTimeout(() => {
      Object.entries(entriesByTopic).forEach(([topicId, entries]) => {
        const sectionId = idToSection.get(String(topicId));
        if (!sectionId) return;
        const numericId = topicIdToNumericId.get(String(topicId));
        if (!numericId) return;
        // Map wire TopicEntry -> BusinessItemDetail shape used by table/grid
        const mapped = (Array.isArray(entries) ? entries : []).map((e: any) => {
          const title = String(e.idea ?? e.title ?? "");
          const actions = String(e.action ?? "");
          const result = String(e.result ?? "");

          // Recompute status based on current content to ensure accuracy
          const backendStatus = (e.status as any) ?? "idea";
          const computedStatus = recomputeEntryStatus(title, actions, result);

          // Debug log when status changes due to recomputation
          if (backendStatus !== computedStatus) {
            console.log(
              `[ProjectMainContent] Status recomputed for entry ${e.id}:`,
              {
                backend: backendStatus,
                computed: computedStatus,
                hasActions: Boolean(actions?.trim()),
                hasResult: Boolean(result?.trim()),
                topicId,
              }
            );
          }

          return {
            id: String(e.id ?? Math.random()),
            title,
            actions,
            result,
            status: computedStatus, // Use recomputed status instead of backend value
            // Pass through AI metadata for research/suggestions rendering
            metadata: (e as any).metadata || undefined,
            // Provide topic id context on the detail for action triggers
            topicId: String(numericId),
          };
        });
        try {
          // Merge with store to avoid overwriting optimistic local changes
          const existing = getTopicEntries(
            sectionId,
            String(numericId)
          ) as any[];
          const mergedMap = new Map<string, any>();
          // Prefer existing store entries (optimistic local state) over backend
          // Keep initial render unfiltered; cleanup will happen downstream in the table view
          (mapped as any[]).forEach((e) => mergedMap.set(String(e.id), e));
          (existing || []).forEach((e) => mergedMap.set(String(e.id), e));
          // Now re-apply store entries to win
          (existing || []).forEach((e) => mergedMap.set(String(e.id), e));
          const merged = Array.from(mergedMap.values());
          // Store under numericTopicId to match grid/item ids
          setTopicEntries(sectionId, String(numericId), merged as any);
        } catch {}
      });
      // Apply entry limits after syncing all entries
      applyEntryLimitsToAllTopics();
      syncTimerRef.current = null;
    }, 200); // small debounce to avoid bursts

    return () => {
      if (syncTimerRef.current) clearTimeout(syncTimerRef.current);
      syncTimerRef.current = null;
    };
  }, [
    allTopics,
    entriesByTopic,
    setTopicEntries,
    applyEntryLimitsToAllTopics,
    getTopicEntries,
    recomputeEntryStatus,
  ]);
  return (
    <div
      className={`flex-1 min-w-0 min-h-0 w-full max-w-full pb-18 overflow-y-auto bg-[var(--background)] relative transition-all duration-300 ease-in-out main-content-bottom-fade ${
        selectedItem ? "p-0" : "p-4"
      }`}
    >
      {/* Background layer with fade effect */}
      <div className="absolute inset-0 project-main-content pointer-events-none " />

      {/* Content layer */}
      <div className="relative z-30">
        {/* Content Section - Drafts and Files */}
        {/* Drafts/Files temporarily hidden until backed by real data */}
        <ContentSections
          activeContent={activeContent}
          setActiveContent={setActiveContent}
          mockDraftItems={[]}
          mockFileItems={[]}
        />

        <AnimatePresence mode="wait">
          {/* Business Sections Grid - Default View */}
          {!activeContent && !selectedItem && (
            <motion.div
              key="business-sections"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              {/* Progress Bar */}
              {!isLoading &&
                !error &&
                !isDependencyLoading &&
                sections.length > 0 && (
                  <div className="project-progress mb-6">
                    <Focusable
                      focusKey="project-progress"
                      level={3}
                      direction="horizontal"
                    >
                      {({ isFocused }) => (
                        <ProgressBar
                          progress={Math.max(
                            0,
                            Math.min(100, Math.round(progress))
                          )}
                        />
                      )}
                    </Focusable>
                  </div>
                )}

              {/* Progress Bar Loading State */}
              {!isLoading && !error && isDependencyLoading && (
                <div className="mb-6">
                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div className="h-full bg-primary animate-pulse"></div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 text-center">
                    Calculating progress...
                  </p>
                </div>
              )}

              {/* Loading State - Business Sections */}
              {isLoading && (
                <div className="flex items-center justify-center min-h-[60vh]">
                  <div className="text-center">
                    <Logo size={64} animated={true} />
                    <p className="text-gray-600 dark:text-gray-400 mt-4 text-sm">
                      Loading business sections...
                    </p>
                  </div>
                </div>
              )}

              {/* Loading State - Topics */}
              {!isLoading && !error && isDependencyLoading && (
                <div className="flex items-center justify-center min-h-[40vh]">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm">
                      Loading project topics...
                    </p>
                  </div>
                </div>
              )}

              {/* Error State */}
              {error && (
                <div className="flex items-center justify-center min-h-[60vh]">
                  <div className="text-center">
                    <div className="text-red-500 mb-4">
                      <Logo size={64} />
                    </div>
                    <p className="text-red-600 dark:text-red-400 text-sm">
                      Error loading business sections: {error}
                    </p>
                  </div>
                </div>
              )}

              {/* Business Sections Grid */}
              {!isLoading && !error && !isDependencyLoading && (
                <div className="business-sections-grid">
                  <Focusable focusKey="business-sections" level={2}>
                    {({ isFocused }) => (
                      <BusinessSectionsGrid
                        onItemClick={onBusinessItemClick}
                        projectId={projectId || ""}
                        allTopics={allTopics}
                      />
                    )}
                  </Focusable>
                </div>
              )}

              {/* Empty State */}
              {!isLoading && !error && sections.length === 0 && (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <p className="text-gray-600 mb-4">
                      No business sections found
                    </p>
                    <Button onClick={() => window.location.reload()}>
                      Reload
                    </Button>
                  </div>
                </div>
              )}

              {/* Topics list below sections */}
              {/* TopicsSection removed; topics now render inside each section */}
            </motion.div>
          )}

          {/* Business Item Detail View */}
          {!activeContent && selectedItem && (
            <motion.div
              key="business-detail"
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -30 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <BusinessItemTable
                itemDetails={itemDetails}
                selectedBusinessItem={selectedItem}
                onBackToItems={onBackToItems}
                sectionId={(selectedItem as any)?.sectionId}
                topicId={selectedItem?.id}
              />
            </motion.div>
          )}

          {/* Placeholder for "files" content */}
          {activeContent === "files" && (
            <motion.div
              key="files-placeholder"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="max-w-4xl mx-auto p-6">
                {/* Header with back button */}
                <div className="flex items-center gap-4 mb-4">
                  <Button
                    variant="outline"
                    onClick={() => setActiveContent(null)}
                    className="flex items-center gap-2"
                  >
                    ← Back
                  </Button>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                    Project Files
                  </h2>
                </div>

                {/* File upload area */}
                <Card className="p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-colors">
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-3">
                      <svg
                        className="w-6 h-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                    </div>
                    <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Upload Files
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Drag and drop files here, or click to browse
                    </p>
                    <Button className="bg-blue-600 hover:bg-blue-700 text-sm">
                      Choose Files
                    </Button>
                  </div>
                </Card>

                {/* File list placeholder */}
                <div className="mt-6">
                  <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Recent Files
                  </h3>
                  <Card className="p-4">
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      <svg
                        className="mx-auto w-10 h-10 mb-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <p className="text-sm">No files uploaded yet</p>
                      <p className="text-xs mt-1">
                        Upload your first file to get started
                      </p>
                    </div>
                  </Card>
                </div>

                {/* File management features */}
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-4 h-4 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Upload
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Drag & drop files
                    </p>
                  </Card>

                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-4 h-4 text-green-600 dark:text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Search
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Find files quickly
                    </p>
                  </Card>

                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-4 h-4 text-purple-600 dark:text-purple-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Organize
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Create folders
                    </p>
                  </Card>
                </div>
              </div>
            </motion.div>
          )}

          {/* Placeholder for "drafts" content */}
          {activeContent === "drafts" && (
            <motion.div
              key="drafts-placeholder"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <div className="max-w-4xl mx-auto p-0">
                {/* Header with back button */}
                <div className="flex items-center gap-4 mb-4">
                  <Button
                    variant="outline"
                    onClick={() => setActiveContent(null)}
                    className="flex items-center gap-2"
                  >
                    ← Back
                  </Button>
                  <h2 className="text-md font-semibold text-gray-900 dark:text-gray-100">
                    Project Drafts
                  </h2>
                </div>

                {/* Drafts creation area */}
                <Card className="p-6 border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 transition-colors">
                  <div className="text-center">
                    <div className="mx-auto w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-3">
                      <svg
                        className="w-6 h-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                    </div>
                    <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Create New Draft
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      Start writing your next project draft
                    </p>
                    <Button className="bg-green-600 hover:bg-green-700 text-sm">
                      Create Draft
                    </Button>
                  </div>
                </Card>

                {/* Drafts list placeholder */}
                <div className="mt-6">
                  <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Recent Drafts
                  </h3>
                  <Card className="p-4">
                    <div className="text-center text-gray-500 dark:text-gray-400">
                      <svg
                        className="mx-auto w-10 h-10 mb-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                      <p className="text-sm">No drafts created yet</p>
                      <p className="text-xs mt-1">
                        Create your first draft to get started
                      </p>
                    </div>
                  </Card>
                </div>

                {/* Draft management features */}
                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-3">
                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-4 h-4 text-green-600 dark:text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Create
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      New draft
                    </p>
                  </Card>

                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-4 h-4 text-blue-600 dark:text-blue-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Edit
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Modify drafts
                    </p>
                  </Card>

                  <Card className="p-3 text-center">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-2">
                      <svg
                        className="w-5 h-5 text-purple-600 dark:text-purple-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-9a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1 text-sm">
                      Organize
                    </h4>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Sort & categorize
                    </p>
                  </Card>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
