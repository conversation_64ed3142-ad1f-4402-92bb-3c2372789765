"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";
import {
  AlertTriangle,
  BarChart3,
  Building,
  Check,
  ChevronUp,
  DollarSign,
  FileText,
  Focus,
  Heart,
  Lightbulb,
  Megaphone,
  MessageCircle,
  Package,
  Palette,
  Settings,
  Shield,
  Star,
  Target,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import { useMemo, useState, useEffect } from "react";
import {
  computeEntryCounts,
  computeTopicStatus,
  mergeEntriesPreferStore,
} from "@/utils/entryCounts";
import { StatusCountBadge } from "@/components/ui/status-count-badge";
import { Focusable } from "@/components/common/Focusable";

import type {
  ProjectTopicSummary,
  TopicEntry,
} from "@/hooks/queries/useTopics";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import { useTopicNewItemsStore } from "@/stores/topicNewItemsStore";

// Stage-based topic filtering (matching useTopics.ts)
// 🚀 Dependency service logic replaces hardcoded stage filters

// Topic icon mapping based on topic titles
const getTopicIcon = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes("problem")) return AlertTriangle;
  if (titleLower.includes("audience") || titleLower.includes("customer"))
    return Users;
  if (titleLower.includes("alternative") || titleLower.includes("competitor"))
    return Lightbulb;
  if (titleLower.includes("market") || titleLower.includes("size"))
    return BarChart3;
  if (titleLower.includes("trend")) return TrendingUp;
  if (titleLower.includes("value") || titleLower.includes("uvp")) return Star;
  if (titleLower.includes("product") || titleLower.includes("feature"))
    return Package;
  if (titleLower.includes("tech") || titleLower.includes("technology"))
    return Settings;
  if (titleLower.includes("positioning")) return Target;
  if (titleLower.includes("marketing") || titleLower.includes("campaign"))
    return Megaphone;
  if (titleLower.includes("brand") || titleLower.includes("design"))
    return Palette;
  if (titleLower.includes("communication") || titleLower.includes("message"))
    return MessageCircle;
  if (titleLower.includes("content") || titleLower.includes("documentation"))
    return FileText;
  if (titleLower.includes("structure") || titleLower.includes("organization"))
    return Building;
  if (
    titleLower.includes("revenue") ||
    titleLower.includes("monetization") ||
    titleLower.includes("pricing")
  )
    return DollarSign;
  if (titleLower.includes("legal") || titleLower.includes("compliance"))
    return Shield;
  if (titleLower.includes("culture") || titleLower.includes("values"))
    return Heart;
  return Lightbulb; // Default fallback
};

// Item Row Component
// TODO: Phase 1 topics should never be disabled - they are always unlocked
const ItemRow = ({
  item,
  onItemClick,
  disabled = false,
}: {
  item: BusinessItem & { entries?: any[] };
  onItemClick: (item: BusinessItem) => void;
  disabled?: boolean;
  subtitle?: string;
  lockedDeps?: string[];
}) => {
  const entries = (item as any).entries || [];
  const markTopicOpened = useTopicNewItemsStore((s) => s.markTopicOpened);

  // Calculate counts from actual entries using shared helper
  const {
    ideas: ideaCount,
    actions: actionCount,
    confirmed: resultCount,
  } = computeEntryCounts(entries as any[]);

  const getStatusStyles = (status: string, disabled: boolean = false) => {
    if (disabled) {
      return "bg-gray-50 dark:bg-[var(--background)]/20 text-gray-400 dark:text-gray-500 opacity-80";
    }

    switch (status) {
      default:
        return "bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300";
    }
  };

  return (
    <div className="mb-3">
      <Focusable focusKey={String(item.title.toLowerCase())} level={5}>
        {({ isFocused }) => (
          <div
            className={`flex items-center justify-between p-3 rounded-lg  border  hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] ${getStatusStyles(
              item.status,
              disabled ?? false
            )} relative z-40 ${
              disabled
                ? "cursor-not-allowed opacity-50 group hover:border-[var(--siift-light-mid)]/50"
                : ""
            } `}
            onClick={() => {
              markTopicOpened(String(item.id));
              onItemClick(item);
            }}
          >
            <div className="flex items-start gap-3">
              <span className="font-sm text-base">{item.title}</span>
            </div>
            {disabled && (
              <div className="absolute inset-0 bg-[var(--siift-light-mid)]/80 rounded-md flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-[9999] ">
                <span className="text-[var(--primary-foreground)] text-md font-bold ">
                  Too Soon
                </span>
              </div>
            )}
            <div
              className={`flex items-center gap-1 flex-shrink-0 ${
                disabled ? "opacity-50" : ""
              }`}
            >
              {entries.length > 0 && (
                <>
                  <StatusCountBadge type="idea" count={ideaCount} />
                  <StatusCountBadge type="action" count={actionCount} />
                  <StatusCountBadge type="confirmed" count={resultCount} />
                </>
              )}
            </div>
          </div>
        )}
      </Focusable>
    </div>
  );
};

// Expandable Card Component
const ExpandableCard = ({
  section,
  onItemClick,
}: {
  section: BusinessSection & { isUnlocked?: boolean };
  onItemClick: (item: BusinessItem) => void;
  projectId: string;
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <Card
      className={`bg-white dark:bg-card border border-[var(--siift-light-mid)]/50dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40 ${
        !section.isUnlocked
          ? "opacity-60 bg-gray-50 dark:bg-[var(--background)]/20"
          : ""
      }`}
    >
      <Focusable focusKey={String(section.title.toLowerCase())} level={3}>
        {({ isFocused }) => (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <CardHeader
                className={`cursor-pointer hover:bg-[var(--siift-light-main)]/30 dark:hover:bg-background transition-colors pb-2 px-3 py-2 my-2 mx-2 rounded-lg `}
              >
                <div className="flex items-center justify-between ">
                  <CardTitle
                    className={`text-md font-semibold text-[var(--siift-darkest)] dark:text-[var(--siift-lightest)]`}
                  >
                    {section.title}
                  </CardTitle>
                  <ChevronUp
                    className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform ${
                      isExpanded ? "rotate-0" : "rotate-180"
                    }`}
                  />
                </div>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent className="py-0 px-3 relative mt-2">
                {/* TODO: Phase 1 topics should never be disabled - disabled={!item.isUnlocked} should always be false for initial stage */}
                {section.items.map((item: any) => (
                  <ItemRow
                    key={item.id}
                    item={{ ...item, sectionId: section.id } as any}
                    disabled={!item.isUnlocked}
                    lockedDeps={item.lockedDependencies}
                    subtitle={item.subtitle}
                    onItemClick={onItemClick}
                  />
                ))}
              </CardContent>
            </CollapsibleContent>
          </Collapsible>
        )}
      </Focusable>
    </Card>
  );
};

interface BusinessSectionsGridProps {
  onItemClick: (item: BusinessItem) => void;
  projectId: string;
  allTopics?: ProjectTopicSummary[];
}

export function BusinessSectionsGrid({
  onItemClick,
  projectId,
  allTopics = [],
}: BusinessSectionsGridProps) {
  // Subscribe to the store to get live sections with entries
  const { sections } = useBusinessSectionStore();

  // Get entries getter from store - single source of truth
  const { getTopicEntries } = useBusinessSectionStore();

  // Create enriched sections with topics as items and their entries
  const enrichedSections = useMemo(() => {
    const result: (BusinessSection & { isUnlocked: boolean })[] = [];

    // Simplified: Store is the single source of truth for entries
    const getEntriesFromStore = (
      sectionKey: string,
      numericTopicId: number
    ): TopicEntry[] => {
      return getTopicEntries(
        sectionKey,
        String(numericTopicId)
      ) as unknown as TopicEntry[];
    };

    // Build a quick lookup from layer -> progress made (action/unproven/confirmed)
    const layerFilledMap = new Map<string, boolean>();

    allTopics.forEach((t) => {
      const sectionKey = t.mappedCategoryId ?? t.sectionId;
      const entries = getEntriesFromStore(sectionKey, t.numericTopicId);

      // Check if topic has progress (any entries with action/confirmed status)
      const hasProgress = entries.some(
        (entry) =>
          entry.status === "action" ||
          entry.status === "confirmed" ||
          entry.status === "unproven"
      );

      if (t.layer) {
        layerFilledMap.set(
          t.layer,
          Boolean(layerFilledMap.get(t.layer) || hasProgress)
        );
      }
    });

    sections.forEach((section) => {
      const sectionTopics = allTopics.filter(
        (t) =>
          (t.mappedCategoryId ?? t.sectionId) === section.id &&
          !t.title.toLowerCase().includes("market size") &&
          !t.mappedLabel?.toLowerCase().includes("market size") &&
          t.layer !== "market-size"
      );

      const normalizeLabel = (label: string) => {
        const base = (label || "")
          .toString()
          .trim()
          .toLowerCase()
          // remove punctuation/specials
          .replace(/[^a-z0-9\s]/g, " ")
          // collapse whitespace
          .replace(/\s+/g, " ")
          .trim();

        // very light singularization (problem(s), competitor(s), idea(s))
        const singular =
          base.endsWith("s") && base.length > 3 ? base.slice(0, -1) : base;

        // synonym collapsing for common topic labels
        const synonyms: Record<string, string> = {
          uvp: "value proposition",
          "unique value proposition": "value proposition",
          customers: "customer",
          audience: "customer",
          competitors: "competitor",
          alternatives: "competitor",
          "market sizing": "market",
          "market size": "market",
          tech: "technology",
        };

        return synonyms[singular] || singular;
      };

      // Group topics by normalized label within this section
      const groups = new Map<string, ProjectTopicSummary[]>();

      sectionTopics.forEach((t) => {
        const key = normalizeLabel(t.mappedLabel || t.title);

        if (!key) return;

        const list = groups.get(key) || [];

        list.push(t);

        groups.set(key, list);
      });

      const topicItems = Array.from(groups.entries()).map(([, group]) => {
        // Choose canonical topic (smallest numeric id)
        const canonicalNumericId = Math.min(
          ...group.map((g) => g.numericTopicId)
        );
        const canonical =
          group.find((g) => g.numericTopicId === canonicalNumericId) ||
          group[0];
        const displayTitle = canonical.mappedLabel || canonical.title;

        // Merge entries across all topics in the group (prefer store versions)
        const entryMap = new Map<string, TopicEntry>();

        group.forEach((t) => {
          const sectionKey = t.mappedCategoryId ?? t.sectionId;
          const entries = getEntriesFromStore(sectionKey, t.numericTopicId);
          entries.forEach((e) => entryMap.set(String(e.id), e));
        });

        const mergedEntries = Array.from(entryMap.values());

        // Ensure we only show the top 3 entries total (store already limits per topic, but we need to limit across group)
        const limitedEntries = mergedEntries
          .sort((a: any, b: any) => {
            const aTime = Date.parse(String(a?.updatedAt || a?.createdAt || 0));
            const bTime = Date.parse(String(b?.updatedAt || b?.createdAt || 0));
            if (Number.isFinite(bTime - aTime) && bTime !== aTime) {
              return bTime - aTime;
            }
            return Number(b?.id) - Number(a?.id);
          })
          .slice(0, 3);

        // Calculate badge counts and status using shared helpers
        const {
          ideas: ideaCount,
          actions: actionCount,
          confirmed: resultCount,
        } = computeEntryCounts(limitedEntries as any[]);
        const topicStatus = computeTopicStatus(limitedEntries as any[]);

        // Union dependencies across the group for locking
        const depSet = new Set<string>();

        const depLabelMap = new Map<string, string>();

        group.forEach((t) => {
          const deps = Array.isArray(t.dependencies) ? t.dependencies : [];
          deps.forEach((dep, idx) => {
            depSet.add(dep);
            const lbl = Array.isArray(t.dependencyLabels)
              ? t.dependencyLabels[idx]
              : undefined;
            if (lbl && !depLabelMap.has(dep)) depLabelMap.set(dep, lbl);
          });
        });
        const deps = Array.from(depSet);
        const missingDeps = deps.filter(
          (dep) => layerFilledMap.get(dep) !== true
        );

        // 🚀 Use dependency service for unlocking (replaces hardcoded stage logic)
        // Unlocking logic: Topics unlock when all their dependencies show progress
        const isUnlocked = deps.length === 0 ? true : missingDeps.length === 0;
        const lockedDependencyLabels = !isUnlocked
          ? missingDeps.map((dep) => depLabelMap.get(dep) || dep)
          : undefined;

        return {
          id: String(canonicalNumericId),
          title: displayTitle,
          status: topicStatus,
          actions: actionCount,
          ideas: ideaCount,
          results: resultCount,
          icon: getTopicIcon(displayTitle),
          entries: limitedEntries,
          isUnlocked,
          lockedDependencies: lockedDependencyLabels,
          subtitle: canonical.description ?? canonical.titlePrompt ?? undefined,
        } as unknown as BusinessItem & { entries: any[] } & {
          isUnlocked: boolean;
          lockedDependencies?: string[];
          subtitle?: string;
        };
      });

      // Final safeguard: remove duplicate items produced by near-identical labels
      const seen = new Set<string>();
      const dedupedItems = (topicItems as unknown as BusinessItem[]).filter(
        (it) => {
          const key = normalizeLabel((it as any)?.title || "");
          if (!key) return true;

          if (seen.has(key)) return false;

          seen.add(key);
          return true;
        }
      ) as unknown as BusinessItem[];

      result.push({
        ...section,
        items: dedupedItems,
        isUnlocked: true, // Sections are always visible; individual topics enforce locking
      });
    });

    console.log("[BusinessSectionsGrid] RENDER:", {
      projectId,
      sectionsCount: result?.length || 0,
      allTopicsCount: allTopics.length,
      sectionsType: typeof result,
      sectionsIsArray: Array.isArray(result),
      enrichedSections: result?.map((s) => ({
        id: s.id,
        title: s.title,
        itemsCount: s.items?.length || 0,
        items: s.items?.map((item) => ({
          id: item.id,
          title: item.title,
          hasEntries: Array.isArray((item as any)?.entries),
          entriesCount: (item as any)?.entries?.length || 0,
        })),
      })),
    });

    return result;
  }, [allTopics, getTopicEntries, sections, projectId]);

  return (
    <div className="w-full relative z-40">
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min">
          {enrichedSections.map((section) => (
            <ExpandableCard
              key={section.id}
              section={section}
              onItemClick={onItemClick}
              projectId={projectId}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
