## Cursor Code Rules: Chat Handling Fragility

These rules guide edits that touch chat, streaming, intake, or suggestion flows. The goal is to avoid regressions where the chat UI gets stuck in “responding”. Keep these in mind for any PR that touches chat hooks, stores, or SSE handling.

### Streaming state ownership
- Only set `setProjectStreaming(projectId, true)` from chat token events that belong to human-facing agents (coach/coaching), not tool/suggestions/siift-generator agents.
- Never rely on a single completion event. Always reset streaming on these events when present: `chat_turn_complete`, `coaching_complete`, `complete`, `stream_complete`, `suggestions_completed`, `suggestions_error`, and generic `error` handlers.
- When adding new SSE event types, explicitly decide whether they should toggle streaming. Default to NOT toggling unless they are producing visible chat tokens.

### Token event filtering
- In `useAiChat` and `useAiIntake`, ignore tokens/streams when `agent` includes any of: `tools`, `siift_generator`, `suggest`.
- Also ignore chunks starting with `TOOL:` when updating the visible chat transcript.

### Abort, cleanup, and reconnection
- Before sending a new message, abort any previous in-flight request and clear token dedupe refs.
- On unmount or navigation, always close the EventSource to prevent ghost streams.
- If the SSE connection errors, clear streaming/loading flags to unblock the UI.

### Topic scoping and onboarding
- Always switch chat scope with `setScope(projectId, topicName)` when a topic is selected.
- Seed per-topic onboarding messages if the topic scope has no prior messages. Persist onboarding per topic slug: `onboarding_messages_${projectId}_topic_${slug}`.
- Send `topic_name` on every chat POST when a topic is active.

### Session handling
- Open SSE before sending a chat so tokens have a stream target.
- Persist/reuse session IDs per project (`chat_session_${projectId}`) to avoid duplicating sessions.

### When adding new features
- New agents/events must specify whether they affect streaming. If they only update data (e.g., suggestions), do not set streaming to true; ensure completion resets streaming.
- Add explicit event listeners and resets if the backend introduces new completion event names.

### Git commit message rules
- NO EMOJIS in commit messages. Keep them clean and professional.
- Use conventional commit format: "fix:", "feat:", "refactor:", etc.

### Quick checklist before merging chat-related changes
- Are non-visible agent tokens filtered out from streaming state?
- Are all relevant completion events resetting streaming?
- Are abort/cleanup paths covered (including error handlers)?
- Does topic chat send `topic_name` and have per-topic onboarding?


